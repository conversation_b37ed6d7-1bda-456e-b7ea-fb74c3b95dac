import asyncio
import json
from sms_parser import SMSParser


# Sample SMS messages for testing
SAMPLE_SMS_MESSAGES = [
    # UPI Transaction
    """UPI txn of Rs 180.0 debited from A/c X4884 on 02May24 to <PERSON><PERSON><PERSON>eeraj J. 
    Ref no ************. Avl bal Rs 2000.""",
    
    # Debit Card Transaction
    """Your Debit Card XX1234 used for Rs 1,250.00 at AMAZON INDIA on 15-05-2024. 
    Txn Ref: *********012. Available Balance: Rs 45,000.00""",
    
    # EMI Payment
    """EMI of Rs 5,500 for Loan A/c ********* paid on 01Jun24. 
    EMI No: 12/36. Interest: Rs 2,100. Late fee: Rs 0. Ref: EMI789012""",
    
    # Salary Credit
    """Salary of Rs 75,000 credited to A/c XX5678 on 01-06-2024 from ABC TECHNOLOGIES PVT LTD. 
    Ref: SAL240601. Available Balance: Rs 1,25,000""",
    
    # Loan Disbursal
    """Loan amount Rs 2,50,000 disbursed to A/c XX9876 on 10May24. 
    Loan ID: LN2024001. Lender: HDFC Bank. Ref: LN789456123""",
    
    # Account Balance Update
    """A/c XX1234 Balance Update: Available Balance Rs 15,750.50 as on 20-05-2024. 
    Account Status: Active. Last Txn: 19-05-2024""",
    
    # Loan Account Status
    """Loan A/c LN567890 Status: Outstanding Amount Rs 1,85,000. 
    Next EMI Due: 05-07-2024. Status: Regular. Lender: SBI""",
    
    # Multiple transactions in one SMS
    """UPI payment Rs 500 to Grocery Store on 25May24. Ref: *********. 
    Card payment Rs 2,000 at Petrol Pump. Card XX5678. Ref: *********. 
    Available Balance: Rs 25,000""",
    
    # Investment Transaction
    """SIP of Rs 5,000 invested in HDFC Equity Fund on 01-06-2024. 
    Folio: 12345/67. Units: 125.50. NAV: Rs 39.84. Ref: SIP240601""",
    
    # Overdue EMI
    """EMI Payment Overdue: Rs 8,500 for Loan XX7890 due on 05-05-2024. 
    Late fee Rs 500 charged. Total Due: Rs 9,000. Pay immediately to avoid penalty."""
]


async def test_sms_parser():
    """Test the SMS parser with sample messages."""
    parser = SMSParser()
    
    print("🔍 Testing SMS Parser with Sample Messages\n")
    print("=" * 80)
    
    for i, sms in enumerate(SAMPLE_SMS_MESSAGES, 1):
        print(f"\n📱 SMS {i}:")
        print(f"Text: {sms[:100]}..." if len(sms) > 100 else f"Text: {sms}")
        print("-" * 40)
        
        try:
            results = await parser.parse_sms(sms)
            
            if results:
                for j, result in enumerate(results, 1):
                    print(f"Result {j}:")
                    print(json.dumps(result, indent=2))
                    print()
            else:
                print("❌ No structured data extracted")
                
        except Exception as e:
            print(f"❌ Error parsing SMS: {str(e)}")
        
        print("-" * 80)


async def test_specific_fields():
    """Test specific field extraction."""
    from field_extractors import FieldExtractor
    
    extractor = FieldExtractor()
    
    print("\n🧪 Testing Specific Field Extraction\n")
    
    # Test amount extraction
    test_texts = [
        "Rs 1,250.50 debited",
        "Amount: INR 5000",
        "₹ 750 paid",
        "Txn of Rs 2,500.00"
    ]
    
    print("💰 Amount Extraction:")
    for text in test_texts:
        amount = await extractor.extract_amount(text)
        print(f"  '{text}' → {amount}")
    
    # Test date extraction
    date_texts = [
        "on 02May24",
        "dated 15-05-2024",
        "on 01 June 2024",
        "2024-06-01"
    ]
    
    print("\n📅 Date Extraction:")
    for text in date_texts:
        date = await extractor.extract_date(text)
        print(f"  '{text}' → {date}")
    
    # Test account number extraction
    account_texts = [
        "A/c X4884",
        "Account No: XX1234",
        "from A/c XXXX5678",
        "debited from XX9876"
    ]
    
    print("\n🏦 Account Number Extraction:")
    for text in account_texts:
        account = await extractor.extract_account_number(text)
        print(f"  '{text}' → {account}")


async def test_classification():
    """Test SMS classification."""
    from classifiers import SMSClassifier
    
    classifier = SMSClassifier()
    
    print("\n🏷️  Testing SMS Classification\n")
    
    test_cases = [
        "UPI payment of Rs 500 to merchant",
        "Debit card used at ATM for Rs 2000",
        "EMI of Rs 5000 paid for loan",
        "Salary Rs 50000 credited to account",
        "Loan amount Rs 100000 disbursed",
        "Account balance Rs 25000 available",
        "Loan outstanding amount Rs 75000"
    ]
    
    for text in test_cases:
        classification = classifier.classify_sms(text)
        print(f"Text: {text}")
        print(f"Classification: {classification}")
        print("-" * 40)


def run_tests():
    """Run all tests."""
    print("🚀 Starting SMS Parser Tests\n")
    
    # Run async tests
    asyncio.run(test_sms_parser())
    asyncio.run(test_specific_fields())
    asyncio.run(test_classification())
    
    print("\n✅ All tests completed!")


if __name__ == "__main__":
    run_tests()
