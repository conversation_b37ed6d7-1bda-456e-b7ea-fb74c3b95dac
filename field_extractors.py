import re
import asyncio
from typing import Optional, List, Dict, Any
from collections import Counter
from utils import normalize_amount, normalize_date, clean_text


class FieldExtractor:
    """
    Field extraction class with multiple regex patterns per field.
    Uses frequency-based matching to select the most confident value.
    """
    
    def __init__(self):
        self._compile_patterns()
    
    def _compile_patterns(self):
        """Compile all regex patterns for better performance."""
        # Amount patterns - improved based on CSV analysis
        self.amount_patterns = [
            re.compile(r"(?:INR|Rs\.?|₹)\s?([\d,]+\.\d{1,2})", re.IGNORECASE),
            re.compile(r"debited by\s*(?:INR|Rs\.?|₹)?\s?([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            re.compile(r"credited (?:with|by)\s*(?:INR|Rs\.?|₹)?\s?([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            re.compile(r"Amount\s*[:\-]?\s*(?:INR|₹)?\s?([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            re.compile(r"Txn of\s*(?:INR|₹|Rs\.?)?\s?([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            re.compile(r"INR\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            re.compile(r"Rs\.?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            re.compile(r"₹\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            re.compile(r"(?:^|\s)Rs\s?([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            re.compile(r"(?:paid|spent|transferred|sent)\s*(?:INR|Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            # New patterns from CSV analysis
            re.compile(r"Rs\s?([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            re.compile(r"RS\.\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            re.compile(r"(?:transaction|payment)\s*(?:of|for)\s*(?:INR|Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            re.compile(r"(?:worth|value)\s*(?:INR|Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            re.compile(r"(?:balance|limit)\s*(?:is|of)?\s*(?:INR|Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            # Additional patterns for complex cases
            re.compile(r"(?:only|just)\s+at\s+(?:Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),  # only at Rs.99
            re.compile(r"(?:premium|price)\s+(?:Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),  # premium Rs. 0.9
            re.compile(r"(?:CM|traded)\s+(?:Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),  # CM Rs 4741.25
            re.compile(r"discount\s+of\s+(?:Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),  # discount of Rs. 200
            # Patterns for remaining failed cases
            re.compile(r"payment\s+of\s+(?:Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),  # payment of Rs.700.0
            re.compile(r"refund\s+of\s+(?:Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),  # refund of Rs. 165.01
            re.compile(r"transaction\s+of\s+(?:Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),  # transaction of Rs. 60.0
            re.compile(r"(?:inr|rs\.?)\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),  # INR 600
            re.compile(r"(?:flat|upto)\s+(?:Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),  # FLAT 400 OFF
            # New patterns for SMS-Data.csv
            re.compile(r"(?:Rs\.?|₹)\s*([\d,]+(?:\.\d{1,2})?)\s+on\s+\w+\s+charged", re.IGNORECASE),  # Rs.95.15 on Zomato charged
            re.compile(r"fund\s+balance\s+(?:Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),  # fund balance Rs.0.67
            re.compile(r"(?:Rs\.?|₹)\s*([\d,]+(?:\.\d{1,2})?)\s+paid\s+at", re.IGNORECASE),  # Rs.20 paid at toll plaza
        ]
        
        # Date patterns - improved based on CSV analysis
        self.date_patterns = [
            re.compile(r"(\d{1,2}[A-Za-z]{3}\d{2,4})", re.IGNORECASE),  # 02May24
            re.compile(r"(\d{1,2}[-/]\d{1,2}[-/]\d{2,4})", re.IGNORECASE),  # 02/05/2024
            re.compile(r"(\d{1,2}\s+[A-Za-z]{3,9}\s+\d{2,4})", re.IGNORECASE),  # 02 May 2024
            re.compile(r"on\s+(\d{1,2}[A-Za-z]{3}\d{2,4})", re.IGNORECASE),
            re.compile(r"dated?\s+(\d{1,2}[-/]\d{1,2}[-/]\d{2,4})", re.IGNORECASE),
            re.compile(r"(\d{4}-\d{2}-\d{2})", re.IGNORECASE),  # 2024-05-02
            re.compile(r"(\d{2}-\d{2}-\d{4})", re.IGNORECASE),  # 02-05-2024
            re.compile(r"at\s+(\d{1,2}:\d{2})\s+on\s+(\d{1,2}[A-Za-z]{3}\d{2,4})", re.IGNORECASE),
            # New patterns from CSV analysis
            re.compile(r"on date\s+(\d{1,2}[A-Za-z]{3}\d{2,4})", re.IGNORECASE),  # on date 30Apr24
            re.compile(r"(\d{1,2}-\d{1,2}-\d{2})", re.IGNORECASE),  # 11-01-25
            re.compile(r"(\d{1,2}/\d{1,2}/\d{4})", re.IGNORECASE),  # 05/02/2024
            re.compile(r"(\d{1,2}[A-Za-z]{3}\d{2})", re.IGNORECASE),  # 31May25
            re.compile(r"as on\s+(\d{1,2}-\d{1,2}-\d{4})", re.IGNORECASE),  # as on 20-05-2024
        ]
        
        # Account number patterns - improved based on CSV analysis
        self.account_patterns = [
            re.compile(r"A/c\s*(?:No\.?|Number)?\s*[:\-]?\s*([X\*]*\d{3,6})", re.IGNORECASE),
            re.compile(r"Account\s*(?:No\.?|Number)?\s*[:\-]?\s*([X\*]*\d{3,6})", re.IGNORECASE),
            re.compile(r"from\s+A/c\s+([X\*]*\d{3,6})", re.IGNORECASE),
            re.compile(r"to\s+A/c\s+([X\*]*\d{3,6})", re.IGNORECASE),
            re.compile(r"(?:debited|credited)\s+from\s+([X\*]*\d{3,6})", re.IGNORECASE),
            re.compile(r"([X\*]+\d{3,6})", re.IGNORECASE),  # Masked account numbers
            re.compile(r"A/c\s+([X\*]*\d{3,6})", re.IGNORECASE),
            # New patterns from CSV analysis
            re.compile(r"A/C\s+([X\*]*\d{3,6})", re.IGNORECASE),  # Uppercase A/C
            re.compile(r"A/cX(\d{3,6})", re.IGNORECASE),  # A/cX4884
            re.compile(r"A/C\s+([X\*]{5,}\d{3,6})", re.IGNORECASE),  # A/C XXXXX22821
            re.compile(r"a/c\s+\*(\d{3,6})", re.IGNORECASE),  # a/c *459553
            re.compile(r"A/C\s+\*{4}(\d{3,6})", re.IGNORECASE),  # A/C ****1772
            re.compile(r"a/c\s+xxx(\d{3,6})", re.IGNORECASE),  # a/c xxx884
        ]
        
        # Transaction reference patterns - fixed extraction issues with better priority
        self.txn_ref_patterns = [
            # Most specific patterns first to avoid partial matches
            re.compile(r"Refno\s+(\d+)", re.IGNORECASE),  # Refno ************
            re.compile(r"Ref\s+no\s+(\d+)", re.IGNORECASE),  # Ref no ************
            re.compile(r"transaction\s+number\s+(\d+)", re.IGNORECASE),  # transaction number 412222902000
            re.compile(r"Ref:\s*([A-Z0-9]+)", re.IGNORECASE),  # Ref: DB170000014491453435
            # Additional patterns for remaining cases
            re.compile(r"receipt\s+(?:no|number)\s*[:\-]?\s*([A-Z0-9]+)", re.IGNORECASE),  # Receipt No: 123456
            re.compile(r"code:\s*([A-Z0-9]+)", re.IGNORECASE),  # Code: WELCOME400
            re.compile(r"UTR\s*[:\-]?\s*([A-Z0-9]+)", re.IGNORECASE),
            re.compile(r"RRN\s*[:\-]?\s*([A-Z0-9]+)", re.IGNORECASE),
            # More general patterns
            re.compile(r"(?:Ref|Reference)\s+([A-Z0-9]{10,})", re.IGNORECASE),
            re.compile(r"UPI\s*Ref\s*[:\-]?\s*([A-Z0-9]+)", re.IGNORECASE),
            re.compile(r"Txn\s*(?:ID|Ref)\s*[:\-]?\s*([A-Z0-9]+)", re.IGNORECASE),
            re.compile(r"Transaction\s*(?:ID|Reference)\s*[:\-]?\s*([A-Z0-9]+)", re.IGNORECASE),
            # Last resort patterns - only match if followed by numbers/alphanumeric
            re.compile(r"Ref\s*(?:No\.?|Number)?\s*[:\-]?\s*([A-Z0-9]{8,})", re.IGNORECASE),
            re.compile(r"Reference\s*(?:No\.?|Number)?\s*[:\-]?\s*([A-Z0-9]{8,})", re.IGNORECASE),
        ]
        
        # Bank name patterns
        self.bank_patterns = [
            re.compile(r"(HDFC|ICICI|SBI|Axis|Kotak|IDFC|Yes Bank|IndusInd|PNB|BOB|Canara|Union|IDBI)", re.IGNORECASE),
            re.compile(r"State Bank of India", re.IGNORECASE),
            re.compile(r"Bank of Baroda", re.IGNORECASE),
            re.compile(r"Punjab National Bank", re.IGNORECASE),
            re.compile(r"([A-Z]{2,}\s+Bank)", re.IGNORECASE),
        ]
        
        # UPI recipient patterns - improved based on CSV analysis
        self.upi_recipient_patterns = [
            re.compile(r"trf to\s+([A-Za-z\s\.]+?)(?:\s+Ref)", re.IGNORECASE),  # trf to SMAAASH Refno
            re.compile(r"to\s+([A-Za-z\s\.]+?)(?:\s*\.|\s+(?:Ref|UPI|on|\d))", re.IGNORECASE),
            re.compile(r"paid to\s+([A-Za-z\s\.]+?)(?:\s*\.|\s+(?:Ref|UPI|on|\d))", re.IGNORECASE),
            re.compile(r"transferred to\s+([A-Za-z\s\.]+?)(?:\s*\.|\s+(?:Ref|UPI|on|\d))", re.IGNORECASE),
            re.compile(r"sent to\s+([A-Za-z\s\.]+?)(?:\s*\.|\s+(?:Ref|UPI|on|\d))", re.IGNORECASE),
            re.compile(r"UPI.*?to\s+([A-Za-z\s\.]+?)(?:\s*\.|\s+(?:Ref|on|\d))", re.IGNORECASE),
            re.compile(r"to\s+([A-Za-z][A-Za-z\s\.]+[A-Za-z])(?:\s*\.)", re.IGNORECASE),
            # New patterns from CSV analysis
            re.compile(r"transfer (?:to|from)\s+([A-Za-z\s\.]+?)(?:\s+Ref)", re.IGNORECASE),
            re.compile(r"(?:by|from)\s+([A-Za-z\s\.]+?)(?:\s+\(Ref)", re.IGNORECASE),  # by NAME (Ref no
        ]
        
        # Card number patterns - improved extraction
        self.card_patterns = [
            re.compile(r"Card\s*(?:No\.?|Number)?\s*[:\-]?\s*([X\*]*\d{4})", re.IGNORECASE),
            re.compile(r"(?:Debit|Credit)\s*Card\s*([X\*]*\d{4})", re.IGNORECASE),
            re.compile(r"([X\*]{8,12}\d{4})", re.IGNORECASE),  # Masked card numbers
            # New patterns for better extraction
            re.compile(r"Card\s+([X\*]*\d{4})", re.IGNORECASE),  # Card X3804
            re.compile(r"card\s+ending\s+(\d{4})", re.IGNORECASE),  # card ending 4465
            re.compile(r"(?:Debit|Credit)\s+Card\s+[X\*]+(\d{4})", re.IGNORECASE),  # Debit Card XX1234
        ]
        
        # Merchant name patterns - improved based on CSV analysis
        self.merchant_patterns = [
            re.compile(r"at\s+([A-Za-z0-9\s&\-\.]+?)(?:\s+on|\s+dated?|\s+Ref|\.|$)", re.IGNORECASE),
            re.compile(r"merchant\s+([A-Za-z0-9\s&\-\.]+?)(?:\s+on|\s+Ref|\.|$)", re.IGNORECASE),
            re.compile(r"(?:spent|paid)\s+at\s+([A-Za-z0-9\s&\-\.]+?)(?:\s+on|\.|$)", re.IGNORECASE),
            # New patterns from CSV analysis
            re.compile(r"at\s+([A-Z0-9\s&\-\.]+?)(?:\s+on|\s+against)", re.IGNORECASE),  # at SPOTIFY SI against
            re.compile(r"done at\s+([A-Za-z0-9\s&\-\.]+?)(?:\s+on)", re.IGNORECASE),  # done at 53574886 on
            re.compile(r"trf to\s+([A-Z\s&\-\.]+?)(?:\s+Ref)", re.IGNORECASE),  # trf to MCDONALDS Refno
            # New patterns for Simpl and other services
            re.compile(r"on\s+([A-Za-z]+)\s+charged\s+via", re.IGNORECASE),  # on Zomato charged via Simpl
            re.compile(r"paid\s+at\s+([A-Za-z\s]+)\s+Toll\s+Plaza", re.IGNORECASE),  # paid at Dukkavanipalem Toll Plaza
        ]
    
    async def _extract_with_patterns(self, text: str, patterns: List[re.Pattern], 
                                   normalizer=None) -> Optional[str]:
        """
        Extract field using multiple patterns and return most frequent match.
        """
        matches = []
        
        for pattern in patterns:
            found = pattern.findall(text)
            if found:
                for match in found:
                    if isinstance(match, tuple):
                        # Handle patterns with multiple groups
                        match = ' '.join(match).strip()
                    matches.append(str(match).strip())
        
        if not matches:
            return None
        
        # Apply normalizer if provided
        if normalizer:
            matches = [normalizer(match) for match in matches if normalizer(match)]
        
        # Return most frequent match
        if matches:
            counter = Counter(matches)
            return counter.most_common(1)[0][0]
        
        return None
    
    async def extract_amount(self, text: str) -> Optional[str]:
        """Extract amount with multiple regex patterns."""
        return await self._extract_with_patterns(text, self.amount_patterns, normalize_amount)
    
    async def extract_date(self, text: str) -> Optional[str]:
        """Extract date with multiple regex patterns."""
        return await self._extract_with_patterns(text, self.date_patterns, normalize_date)
    
    async def extract_account_number(self, text: str) -> Optional[str]:
        """Extract account number with multiple regex patterns."""
        return await self._extract_with_patterns(text, self.account_patterns)
    
    async def extract_transaction_ref(self, text: str) -> Optional[str]:
        """Extract transaction reference with multiple regex patterns."""
        return await self._extract_with_patterns(text, self.txn_ref_patterns)
    
    async def extract_bank_name(self, text: str) -> Optional[str]:
        """Extract bank name with multiple regex patterns."""
        return await self._extract_with_patterns(text, self.bank_patterns)
    
    async def extract_upi_recipient(self, text: str) -> Optional[str]:
        """Extract UPI recipient name with multiple regex patterns."""
        result = await self._extract_with_patterns(text, self.upi_recipient_patterns)
        if result:
            # Clean up recipient name
            result = re.sub(r'\s+', ' ', result).strip()
            # Remove common suffixes
            result = re.sub(r'\s+(Ref|UPI|on|\d+).*$', '', result, flags=re.IGNORECASE)
        return result
    
    async def extract_card_number(self, text: str) -> Optional[str]:
        """Extract card number with multiple regex patterns."""
        return await self._extract_with_patterns(text, self.card_patterns)
    
    async def extract_merchant_name(self, text: str) -> Optional[str]:
        """Extract merchant name with multiple regex patterns."""
        result = await self._extract_with_patterns(text, self.merchant_patterns)
        if result:
            # Clean up merchant name
            result = re.sub(r'\s+', ' ', result).strip()
            result = re.sub(r'\s+(on|dated?|Ref).*$', '', result, flags=re.IGNORECASE)
        return result
    
    async def extract_currency(self, text: str) -> Optional[str]:
        """Extract currency information."""
        currency_patterns = [
            re.compile(r"(INR)", re.IGNORECASE),
            re.compile(r"(₹)", re.IGNORECASE),
            re.compile(r"(Rs\.?)", re.IGNORECASE),
        ]
        
        result = await self._extract_with_patterns(text, currency_patterns)
        if result:
            if result.lower() in ['rs', 'rs.', '₹']:
                return 'INR'
            return result.upper()
        
        # Default to INR for Indian SMS
        return 'INR'

    # EMI-specific extractors
    async def extract_emi_amount(self, text: str) -> Optional[str]:
        """Extract EMI amount."""
        emi_patterns = [
            re.compile(r"EMI\s*(?:of|amount)?\s*(?:INR|Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            re.compile(r"installment\s*(?:of|amount)?\s*(?:INR|Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            re.compile(r"monthly\s*payment\s*(?:of)?\s*(?:INR|Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
        ]
        return await self._extract_with_patterns(text, emi_patterns, normalize_amount)

    async def extract_emi_number(self, text: str) -> Optional[str]:
        """Extract EMI number/installment number."""
        emi_num_patterns = [
            re.compile(r"EMI\s*(?:No\.?|Number)?\s*[:\-]?\s*(\d+)", re.IGNORECASE),
            re.compile(r"installment\s*(?:No\.?|Number)?\s*[:\-]?\s*(\d+)", re.IGNORECASE),
            re.compile(r"(\d+)(?:st|nd|rd|th)\s*EMI", re.IGNORECASE),
        ]
        return await self._extract_with_patterns(text, emi_num_patterns)

    async def extract_due_date(self, text: str) -> Optional[str]:
        """Extract due date."""
        due_date_patterns = [
            re.compile(r"due\s*(?:on|date)?\s*(\d{1,2}[A-Za-z]{3}\d{2,4})", re.IGNORECASE),
            re.compile(r"due\s*(?:on|date)?\s*(\d{1,2}[-/]\d{1,2}[-/]\d{2,4})", re.IGNORECASE),
            re.compile(r"payment\s*due\s*(\d{1,2}[A-Za-z]{3}\d{2,4})", re.IGNORECASE),
        ]
        return await self._extract_with_patterns(text, due_date_patterns, normalize_date)

    async def extract_interest_charged(self, text: str) -> Optional[str]:
        """Extract interest charged."""
        interest_patterns = [
            re.compile(r"interest\s*(?:charged|amount)?\s*(?:INR|Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            re.compile(r"interest\s*[:\-]\s*(?:INR|Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
        ]
        return await self._extract_with_patterns(text, interest_patterns, normalize_amount)

    async def extract_late_fee(self, text: str) -> Optional[str]:
        """Extract late fee charged."""
        late_fee_patterns = [
            re.compile(r"late\s*fee\s*(?:charged|amount)?\s*(?:INR|Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            re.compile(r"penalty\s*(?:charged|amount)?\s*(?:INR|Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            re.compile(r"overdue\s*(?:charges?|fee)\s*(?:INR|Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
        ]
        return await self._extract_with_patterns(text, late_fee_patterns, normalize_amount)

    async def extract_loan_repayment_flag(self, text: str) -> Optional[bool]:
        """Check if this is a loan repayment."""
        loan_keywords = ['loan', 'EMI', 'installment', 'repayment', 'mortgage']
        text_lower = text.lower()
        return any(keyword in text_lower for keyword in loan_keywords)

    async def extract_loan_delayed_flag(self, text: str) -> Optional[bool]:
        """Check if loan payment is delayed."""
        delay_keywords = ['overdue', 'late', 'delayed', 'penalty', 'past due']
        text_lower = text.lower()
        return any(keyword in text_lower for keyword in delay_keywords)

    # Loan-specific extractors
    async def extract_loan_id(self, text: str) -> Optional[str]:
        """Extract loan ID."""
        loan_id_patterns = [
            re.compile(r"loan\s*(?:ID|No\.?|Number)?\s*[:\-]?\s*([A-Z0-9]+)", re.IGNORECASE),
            re.compile(r"loan\s*account\s*([A-Z0-9]+)", re.IGNORECASE),
            re.compile(r"application\s*(?:ID|No\.?)?\s*[:\-]?\s*([A-Z0-9]+)", re.IGNORECASE),
        ]
        return await self._extract_with_patterns(text, loan_id_patterns)

    async def extract_lender_name(self, text: str) -> Optional[str]:
        """Extract lender name."""
        lender_patterns = [
            re.compile(r"from\s+([A-Za-z\s&]+?)(?:\s+(?:Bank|Finance|Loan|on|\d))", re.IGNORECASE),
            re.compile(r"lender\s*[:\-]?\s*([A-Za-z\s&]+?)(?:\s+(?:on|\d|\.|$))", re.IGNORECASE),
            re.compile(r"(Bajaj|Tata|HDFC|ICICI|SBI|Axis)\s*(?:Finance|Capital|Finserv)", re.IGNORECASE),
        ]
        return await self._extract_with_patterns(text, lender_patterns)

    # Salary-specific extractors
    async def extract_employer_name(self, text: str) -> Optional[str]:
        """Extract employer name."""
        employer_patterns = [
            re.compile(r"salary\s*(?:from|by)\s+([A-Za-z\s&\.]+?)(?:\s+(?:on|\d|\.|$))", re.IGNORECASE),
            re.compile(r"credited\s*(?:from|by)\s+([A-Za-z\s&\.]+?)(?:\s+(?:on|\d|\.|$))", re.IGNORECASE),
            re.compile(r"employer\s*[:\-]?\s*([A-Za-z\s&\.]+?)(?:\s+(?:on|\d|\.|$))", re.IGNORECASE),
        ]
        return await self._extract_with_patterns(text, employer_patterns)

    async def extract_salary_flag(self, text: str) -> Optional[bool]:
        """Check if this is a salary credit."""
        salary_keywords = ['salary', 'sal', 'payroll', 'wages', 'monthly credit']
        text_lower = text.lower()
        return any(keyword in text_lower for keyword in salary_keywords)

    # Account-specific extractors
    async def extract_account_status(self, text: str) -> Optional[str]:
        """Extract account status."""
        status_patterns = [
            re.compile(r"account\s*(?:is)?\s*(active|inactive|closed|blocked|frozen)", re.IGNORECASE),
            re.compile(r"status\s*[:\-]?\s*(active|inactive|closed|blocked|frozen)", re.IGNORECASE),
        ]
        return await self._extract_with_patterns(text, status_patterns)

    async def extract_account_sub_type(self, text: str) -> Optional[str]:
        """Extract account sub type."""
        subtype_patterns = [
            re.compile(r"(savings|current|salary|fixed deposit|FD|RD)\s*account", re.IGNORECASE),
            re.compile(r"account\s*type\s*[:\-]?\s*(savings|current|salary)", re.IGNORECASE),
        ]
        return await self._extract_with_patterns(text, subtype_patterns)

    async def extract_account_opening_date(self, text: str) -> Optional[str]:
        """Extract account opening date."""
        opening_patterns = [
            re.compile(r"opened\s*(?:on)?\s*(\d{1,2}[A-Za-z]{3}\d{2,4})", re.IGNORECASE),
            re.compile(r"account\s*opened\s*(\d{1,2}[-/]\d{1,2}[-/]\d{2,4})", re.IGNORECASE),
        ]
        return await self._extract_with_patterns(text, opening_patterns, normalize_date)

    async def extract_current_balance(self, text: str) -> Optional[str]:
        """Extract current balance."""
        balance_patterns = [
            re.compile(r"(?:Avl|Available)\s*bal(?:ance)?\s*(?:INR|Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            re.compile(r"balance\s*(?:INR|Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            re.compile(r"current\s*balance\s*(?:INR|Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
        ]
        return await self._extract_with_patterns(text, balance_patterns, normalize_amount)

    async def extract_outstanding_amount(self, text: str) -> Optional[str]:
        """Extract outstanding loan amount."""
        outstanding_patterns = [
            re.compile(r"outstanding\s*(?:amount)?\s*(?:INR|Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            re.compile(r"remaining\s*(?:amount)?\s*(?:INR|Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            re.compile(r"due\s*(?:amount)?\s*(?:INR|Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
        ]
        return await self._extract_with_patterns(text, outstanding_patterns, normalize_amount)

    async def extract_default_status(self, text: str) -> Optional[str]:
        """Extract loan default status."""
        default_patterns = [
            re.compile(r"(default|defaulted|NPA|overdue)", re.IGNORECASE),
            re.compile(r"payment\s*(overdue|delayed|missed)", re.IGNORECASE),
        ]
        result = await self._extract_with_patterns(text, default_patterns)
        return result.lower() if result else None

    # Investment-specific extractors
    async def extract_trading_account(self, text: str) -> Optional[str]:
        """Extract trading account ID."""
        trading_patterns = [
            re.compile(r"Dear\s+([A-Z0-9]+),", re.IGNORECASE),  # Dear KABXXXXX3A,
            re.compile(r"account\s+([A-Z0-9]+)", re.IGNORECASE),
        ]
        return await self._extract_with_patterns(text, trading_patterns)

    async def extract_traded_value(self, text: str) -> Optional[str]:
        """Extract traded value amount."""
        traded_patterns = [
            re.compile(r"traded\s+value.*?(?:Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
            re.compile(r"CM\s+(?:Rs\.?|₹)?\s*([\d,]+(?:\.\d{1,2})?)", re.IGNORECASE),
        ]
        return await self._extract_with_patterns(text, traded_patterns, normalize_amount)

    async def extract_trading_date(self, text: str) -> Optional[str]:
        """Extract trading date."""
        trading_date_patterns = [
            re.compile(r"for\s+(\d{1,2}-[A-Z]{3}-\d{2})", re.IGNORECASE),  # for 17-MAY-24
            re.compile(r"(\d{1,2}-[A-Z]{3}-\d{4})", re.IGNORECASE),  # 17-MAY-2024
        ]
        return await self._extract_with_patterns(text, trading_date_patterns, normalize_date)
