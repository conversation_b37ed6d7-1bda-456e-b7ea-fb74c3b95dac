[{"description": "SMS from JX-JioSvc", "input": "For seamless data experience across the country, set the 'Data Roaming' as On/Always.\nTo know 'How to Set up Jio Network' on mobile, click https://youtu.be/o18LboDi1ho\nTo know your number, track balance & usage, give a miss call to 1299.", "sender": "JX-JioSvc", "timestamp": "Apr 30, 2024 5:59:33 PM", "expected_fields": {}}, {"description": "SMS from +************", "input": "SBIUPI R8ibGLnAIOCkH5F3TOh2ai%2FMgk1CzypJeVUHtkLHjSsf8RiDpv8Upwuw0NgiaKPv!99998f39bf2c7de609a84c5020caa85f05b4b7d82413e655b373d905f1493805", "sender": "+************", "timestamp": "Apr 30, 2024 7:00:06 PM", "expected_fields": {"date": "20caa85", "sms_event_subtype": "UPI"}}, {"description": "SMS from +************", "input": "HDFCUPI 1MEEgA0jaQzrVDM9ELtEcztxficPxr07vSVy9RIV7g3BGXIeJg3VcE0Yas63ksWd", "sender": "+************", "timestamp": "Apr 30, 2024 7:00:06 PM", "expected_fields": {"date": "0Yas63", "sms_event_subtype": "UPI"}}, {"description": "SMS from JM-HDFCBN", "input": "Notice!\nGoogle Pay has initiated UPI registration for your Bank A/c.\nYou will receive your UPI address shortly.\n- HDFC Bank", "sender": "JM-HDFCBN", "timestamp": "Apr 30, 2024 7:00:09 PM", "expected_fields": {"sms_event_subtype": "UPI"}}, {"description": "SMS from AD-HDFCBN", "input": "UPI Activated!\nYou've linked your Bank A/c to UPI on Google Pay.\nNEVER share OTP, PIN, card info.\n- HDFC Bank", "sender": "AD-HDFCBN", "timestamp": "Apr 30, 2024 7:00:19 PM", "expected_fields": {"sms_event_subtype": "UPI"}}, {"description": "SMS from JD-SBIUPI", "input": "Dear UPI user A/C X4884 debited by 2000.0 on date 30Apr24 trf to SMAAASH Refno ************. If not u? call **********. -SBI", "sender": "JD-SBIUPI", "timestamp": "Apr 30, 2024 8:15:23 PM", "expected_fields": {"account_number": "X4884", "txn_ref": "************", "date": "30Apr24", "sms_info_type": "Outflow", "sms_event_subtype": "UPI"}}, {"description": "SMS from JM-RBLBNK", "input": "Cash payment of Rs.2670 received on 30-04-2024 for Loan/Card (XX1772) Ref: ********************, will reflect in your account in 2 working days-RBL Bank", "sender": "JM-RBLBNK", "timestamp": "Apr 30, 2024 9:07:36 PM", "expected_fields": {"amount": "2670", "txn_ref": "********************", "sms_event_subtype": "Debit Card"}}, {"description": "SMS from JM-SBICRD", "input": "Never share your Card details or Mobile No. while raising complaint/query on social media platforms. This information can be misused by fraudsters - SBI Card", "sender": "JM-SBICRD", "timestamp": "Apr 30, 2024 9:11:18 PM", "expected_fields": {"sms_event_subtype": "Debit Card"}}, {"description": "SMS from VM-SBICRD", "input": "Get an Amazon Voucher worth Rs.500 with every Add-on Card. Apply before 30 June 2024 to avail offer. Apply: https://sbicard.com/addon T&C - SBI Card", "sender": "VM-SBICRD", "timestamp": "Apr 30, 2024 9:20:23 PM", "expected_fields": {"amount": "500", "sms_event_subtype": "Debit Card"}}, {"description": "SMS from AX-SBIUPI", "input": "Dear UPI user A/C X4884 debited by 135.46 on date 30Apr24 trf to MCDONALDS Refno ************. If not u? call **********. -SBI", "sender": "AX-SBIUPI", "timestamp": "Apr 30, 2024 9:44:24 PM", "expected_fields": {"account_number": "X4884", "txn_ref": "************", "date": "30Apr24", "sms_info_type": "Outflow", "sms_event_subtype": "UPI"}}, {"description": "SMS from VM-ATMSBI", "input": "Dear Customer,you have done 1 out of 3/5 free transactions at Non-SBI ATMs in Metro/Non-Metro centers this month.Charges applicable beyond free transactions.", "sender": "VM-ATMSBI", "timestamp": "Apr 30, 2024 11:42:36 PM", "expected_fields": {}}, {"description": "SMS from AD-SBIUPI", "input": "Dear UPI user A/C X4884 debited by 25.0 on date 30Apr24 trf to MS MOHAN SINGH Refno ************. If not u? call **********. -SBI", "sender": "AD-SBIUPI", "timestamp": "Apr 30, 2024 11:48:29 PM", "expected_fields": {"account_number": "X4884", "txn_ref": "************", "date": "30Apr24", "sms_info_type": "Outflow", "sms_event_subtype": "UPI"}}, {"description": "SMS from AX-AXISBK", "input": "UPI registration on Google Pay has started. Do not share Debit Card details/OTP/expiry date to avoid financial loss. Not you? Report to your bank - Axis Bank", "sender": "AX-AXISBK", "timestamp": "May 1, 2024 1:36:47 AM", "expected_fields": {"sms_event_subtype": "UPI"}}, {"description": "SMS from AD-PANTLS", "input": "At Pantaloons we have got ur Fashion needs covered!\nClick tlux.in/om2tK & shop online on our website\nor tlux.in/om2tL to download APP & stay connected to fashion 24x7\nGet Extra 10% OFF on ur 1st online bill. Use code PTWELCOME10\nTC", "sender": "AD-PANTLS", "timestamp": "May 1, 2024 10:06:15 AM", "expected_fields": {}}, {"description": "SMS from JX-620016", "input": "Your family member's Jio plan has expired. To continue their services,recharge ********** with Rs.239 plan on PhonePe. Get upto Rs. 500 Rewards each on first 3 recharges by paying via UPI & stay connected always! T&CA. Click https://phon.pe/jiog", "sender": "JX-620016", "timestamp": "May 1, 2024 12:33:56 PM", "expected_fields": {"amount": "239", "sms_event_subtype": "UPI"}}, {"description": "SMS from JD-SWIGGY", "input": "Use OTP 965945 to log into your Swiggy account. Do not share the OTP or your number with anyone including Swiggy personnel. ASkSzFWMk3x", "sender": "JD-SWIGGY", "timestamp": "May 1, 2024 2:32:27 PM", "expected_fields": {}}, {"description": "SMS from ***********", "input": "CREDUPI ppA9bayG0ZZX9zQaLy11*cO4GdxIiviu--t&0", "sender": "***********", "timestamp": "May 1, 2024 6:25:33 PM", "expected_fields": {"sms_event_subtype": "UPI"}}, {"description": "SMS from TX-SBIUPI", "input": "Dear UPI user A/C X4884 debited by 240.0 on date 01May24 trf to SHAILES PRATAP S Refno ************. If not u? call **********. -SBI", "sender": "TX-SBIUPI", "timestamp": "May 1, 2024 6:54:31 PM", "expected_fields": {"account_number": "X4884", "txn_ref": "************", "date": "01May24", "sms_info_type": "Outflow", "sms_event_subtype": "UPI"}}, {"description": "SMS from JD-ATMSBI", "input": "Dear Customer, transaction number ************ for Rs.6026.00 by SBI Debit Card X3804 done at ******** on 01May24 at 22:01:31. Your updated available balance is Rs.32925.97. If not done by you, forward this SMS to **********/ call **********/********** to block card. GOI helpline for cyber fraud 1930.", "sender": "JD-ATMSBI", "timestamp": "May 1, 2024 10:01:59 PM", "expected_fields": {"amount": "6026.00", "date": "01May24", "sms_event_subtype": "Debit Card"}}, {"description": "SMS from VM-SBIUPI", "input": "Dear SBI UPI User, ur A/cX4884 credited by Rs6000 on 01May24 by  (Ref no ************)", "sender": "VM-SBIUPI", "timestamp": "May 1, 2024 10:07:54 PM", "expected_fields": {"amount": "6000", "account_number": "X4884", "txn_ref": "no", "date": "01May24", "sms_info_type": "Inflow", "sms_event_subtype": "UPI"}}, {"description": "SMS from JK-SBIUPI", "input": "Dear UPI user A/C X4884 debited by 180.0 on date 02May24 trf to <PERSON><PERSON>raj Neeraj J Refno ************. If not u? call **********. -SBI", "sender": "JK-SBIUPI", "timestamp": "May 2, 2024 11:47:15 AM", "expected_fields": {"account_number": "X4884", "txn_ref": "************", "date": "02May24", "sms_info_type": "Outflow", "sms_event_subtype": "UPI"}}, {"description": "SMS from JM-620016", "input": "Your family member's Jio plan has expired. To continue their services,recharge ********** with Rs.239 plan on PhonePe. Get upto Rs. 500 Rewards each on first 3 recharges by paying via UPI & stay connected always! T&CA. Click https://phon.pe/jiog", "sender": "JM-620016", "timestamp": "May 2, 2024 12:31:29 PM", "expected_fields": {"amount": "239", "sms_event_subtype": "UPI"}}, {"description": "SMS from AX-BWKOOF", "input": "<PERSON> <PERSON><PERSON><PERSON>, 40-70% OFF LIVE Now at May Summertime Madness!\n\nUse SUMMER15 | Get 15% Cashback\nShop Summer must-haves Now.\n\nOnly on Bewakoof: \nbwkoof.com/jy", "sender": "AX-BWKOOF", "timestamp": "May 2, 2024 12:46:53 PM", "expected_fields": {}}, {"description": "SMS from VK-SBIUPI", "input": "Dear UPI user A/C X4884 debited by 300.0 on date 02May24 trf to ANSHUL  AGARWAL Refno ************. If not u? call **********. -SBI", "sender": "VK-SBIUPI", "timestamp": "May 2, 2024 1:46:54 PM", "expected_fields": {"account_number": "X4884", "txn_ref": "************", "date": "02May24", "sms_info_type": "Outflow", "sms_event_subtype": "UPI"}}, {"description": "SMS from VM-NSESMS", "input": "ZERODHA BROKING LIMITED on 06-04-24 reported your Fund bal Rs.0 & Securities bal 0. This excludes your Bank, DP & PMS bal with the broker-NSE", "sender": "VM-NSESMS", "timestamp": "May 2, 2024 8:29:52 PM", "expected_fields": {"amount": "0"}}, {"description": "SMS from AX-FLPKRT", "input": "Flipkart: Use OTP 624944 to log in to your account. DO NOT SHARE this code with anyone, including the delivery executive. @www.flipkart.com #624944", "sender": "AX-FLPKRT", "timestamp": "May 3, 2024 2:22:54 PM", "expected_fields": {}}, {"description": "SMS from JW-JioPay", "input": "50% Daily Data quota used as on 03-May-24 14:43. \nJio Number : ********** \nDaily Data quota as per plan : 1.50 GB \nTo know more on how to manage your account through MyJio app, click https://youtu.be/nkg_fLUUxD8 Dial 1991, to know your current balance, validity, plan details and for exciting recharge plans.", "sender": "JW-JioPay", "timestamp": "May 3, 2024 2:43:09 PM", "expected_fields": {}}, {"description": "SMS from VM-SBYONO", "input": "809949 is your OTP for registering YONO SBI mobile banking app. Do not share it with anyone. If not done by you, Pls call ********** -YONO, SBI\nmMW8GBrsvJF", "sender": "VM-SBYONO", "timestamp": "May 4, 2024 2:12:18 PM", "expected_fields": {}}, {"description": "SMS from CP-BIGBKT", "input": "Dear bigbasketeer, \nYour voucher is here! You get an instant discount of Rs. 200 today! Find your coupon inside. Shop now u1.mnge.co/AmrllAO", "sender": "CP-BIGBKT", "timestamp": "May 4, 2024 3:08:23 PM", "expected_fields": {"amount": "200"}}, {"description": "SMS from VM-LNKART", "input": "Dear Customer, Buy1 Get1 FREE with Lenskart GOLD membership on luxe frames designed in Paris, from Le Petit Lunetier. TnC\n\nStore lskt.me/m8\n\nApp lskt.me/le", "sender": "VM-LNKART", "timestamp": "May 4, 2024 5:20:56 PM", "expected_fields": {}}, {"description": "SMS from JM-MOMSCO", "input": "(1) Order: Out for delivery TODAY. Feels good right?\nOrder The Moms Co. Vita Rich Serum + Skincare Kit worth Rs.997 only at Rs.99.\nShop Now: weurl.co/yDCenU", "sender": "JM-MOMSCO", "timestamp": "May 5, 2024 3:56:58 PM", "expected_fields": {"amount": "997"}}, {"description": "SMS from VK-SBICRD", "input": "Dear Cardholder, your payment of USD 23.60 at OpenAILLC is due on 06/05/2024 and will be processed through your credit card ending 4465 as per e-Mandate (SiHub ID: X3dKeaZgvM) registered by you. To opt out of this e-Mandate, please log in to www.sbicard.com/emandates. In case, international Online usage on your card is inactive, please enable it by visiting https://sbicard.com/manage-card-usage to prevent any transaction decline - SBI Card", "sender": "VK-SBICRD", "timestamp": "May 5, 2024 8:59:33 PM", "expected_fields": {"sms_event_subtype": "Debit Card"}}, {"description": "SMS from CP-PRACTO", "input": "635111 is the OTP for accessing your Practo account, valid for 30 mins. PLS DO NOT SHARE IT WITH ANYONE\n- Practo", "sender": "CP-PRACTO", "timestamp": "May 6, 2024 4:19:58 PM", "expected_fields": {}}, {"description": "SMS from VK-NSESMS", "input": "ZERODHA BROKING LIMITED on 30-03-24 reported your Fund bal Rs.0 & Securities bal 0. This excludes your Bank, DP & PMS bal with the broker-NSE", "sender": "VK-NSESMS", "timestamp": "May 6, 2024 5:47:18 PM", "expected_fields": {"amount": "0"}}, {"description": "SMS from JD-MMTRIP", "input": "[#] 579990 is the OTP to login into your MakeMyTrip account. We don't ask for your OTP/bank info. Don't share it with anyone. Fkkno1xnYMa - MMT", "sender": "JD-MMTRIP", "timestamp": "May 7, 2024 4:38:32 PM", "expected_fields": {}}, {"description": "SMS from JD-RBLBNK", "input": "Your RBL Bank Loan A/C ****1772 has an overdue amt of Rs. 2661 for the past 24 days. We request you to clear the total overdue amount at the earliest. Please note delayed payments are reported to credit bureaus. For assistance, call 022 ********. -RBL bank", "sender": "JD-RBLBNK", "timestamp": "May 8, 2024 12:31:10 PM", "expected_fields": {"amount": "2661", "account_number": "****1772"}}, {"description": "SMS from TM-FLPKRT", "input": "[#] 454511 is the OTP to log in to your Flipkart account. DO NOT share this code with anyone including Flipkart delivery agents.\nDf9YrqIZHWd", "sender": "TM-FLPKRT", "timestamp": "May 8, 2024 8:21:42 PM", "expected_fields": {}}, {"description": "SMS from JM-ROYSUN", "input": "DEAR CUSTOMER, PNR **********; PA INSURANCE CERTIFICATE/S MAILED; PREMIUM RS. 0.9. FOR POLICY & TO UPDATE NOMINEE VISIT https://irctc.royalsundaram.in", "sender": "JM-ROYSUN", "timestamp": "May 9, 2024 8:29:02 AM", "expected_fields": {"amount": "0.9"}}, {"description": "SMS from **********", "input": "YESPRODUPI pp2)ub6QDh1EIuzmXoAJBr-0?TmUX*(pVn6XJ", "sender": "**********", "timestamp": "May 9, 2024 10:07:26 AM", "expected_fields": {"sms_event_subtype": "UPI"}}, {"description": "SMS from AD-GROWWZ", "input": "Dear Customer, we got a request for registering your account for UPI Groww app.\nDo not share your Debit card details/One time password or OTP/Expiry date number to avoid any financial loss. If it is not initiated by you, please report immediately to your bank helpline number.", "sender": "AD-GROWWZ", "timestamp": "May 9, 2024 10:07:35 AM", "expected_fields": {"sms_event_subtype": "UPI"}}, {"description": "SMS from TM-IRSMSa", "input": "PNR-**********\nTrn:12419\nDt:10-05-24\nFrm PNKD to ALJN\nCls:2A\nP1-WL,2\nChart Prepared\nYour Tickets are not confirmed, automatic refund processed.\nFor Enquiry/Complaint/Assistance,please dial 139 IR-CRIS", "sender": "TM-IRSMSa", "timestamp": "May 10, 2024 3:46:44 AM", "expected_fields": {"txn_ref": "und"}}, {"description": "SMS from JM-SBIUPI", "input": "Dear UPI user A/C X4884 debited by 600.0 on date 10May24 trf to ANSHUL  AGARWAL Refno ************. If not u? call **********. -SBI", "sender": "JM-SBIUPI", "timestamp": "May 10, 2024 6:53:14 AM", "expected_fields": {"account_number": "X4884", "txn_ref": "************", "date": "10May24", "sms_info_type": "Outflow", "sms_event_subtype": "UPI"}}, {"description": "SMS from CP-BMSHOW", "input": "Confirmed! Booking ID TFA7GK4\nClick for ticket/s, offers and details:https://in.bookmyshow.com/tiny/eoNdcv9854\n- BookMyShow", "sender": "CP-BMSHOW", "timestamp": "May 10, 2024 2:50:56 PM", "expected_fields": {}}, {"description": "SMS from JM-NSESMS", "input": "ZERODHA BROKING LIMITED on 13-04-24 reported your Fund bal Rs.0 & Securities bal 0. This excludes your Bank, DP & PMS bal with the broker-NSE", "sender": "JM-NSESMS", "timestamp": "May 10, 2024 7:55:23 PM", "expected_fields": {"amount": "0"}}, {"description": "SMS from JX-IRCTCi", "input": "PNR ********** cancelled being Waitlist after chart preparation, Amount 695 will be refunded in your account within 3-4 days.-IRCTC", "sender": "JX-IRCTCi", "timestamp": "May 11, 2024 5:59:02 AM", "expected_fields": {"txn_ref": "unded"}}, {"description": "SMS from AX-197007", "input": "Your happiness keeps us going. So here's Rs.20 Cashback, only for you. Pay using Paytm UPI & avail now. https://p.paytm.me/xCTH/SC20", "sender": "AX-197007", "timestamp": "May 11, 2024 12:52:01 PM", "expected_fields": {"amount": "20", "sms_event_subtype": "UPI"}}, {"description": "SMS from JX-SBIUPI", "input": "Dear UPI user A/C X4884 debited by 80.0 on date 11May24 trf to DMRC Limited Refno ************. If not u? call **********. -SBI", "sender": "JX-SBIUPI", "timestamp": "May 11, 2024 2:43:01 PM", "expected_fields": {"account_number": "X4884", "txn_ref": "************", "date": "11May24", "sms_info_type": "Outflow", "sms_event_subtype": "UPI"}}, {"description": "SMS from BZ-SBIBNK", "input": "Dear customer, SBI Online Dhamaka is LIVE! Use your SBI Debit Card for online shopping across select categories & earn 2X Points till 31st May. T&C-SBI.", "sender": "BZ-SBIBNK", "timestamp": "May 12, 2024 8:03:05 AM", "expected_fields": {"sms_event_subtype": "Debit Card"}}, {"description": "SMS from CP-GROWWW", "input": "KYC pending. Finish it with Digilocker, so you can proceed to create your demat account. \n\nComplete now: weurl.co/SDnAE5\n\n- Groww", "sender": "CP-GROWWW", "timestamp": "May 17, 2024 8:01:17 AM", "expected_fields": {}}, {"description": "SMS from VA-SBICRD", "input": "Get A/C summary, information related to your SBI Creditcard via website https://www.sbicard.com/creditcards/app/myaccount or App https://sbicard.com/mobile-app", "sender": "VA-SBICRD", "timestamp": "May 17, 2024 9:27:59 AM", "expected_fields": {"sms_event_subtype": "Debit Card"}}]