{"total_tests": 14, "passed": 7, "failed": 7, "test_results": [{"test_number": 1, "name": "SBI UPI Debit - Standard Format", "status": "PASS", "expected": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Outflow", "amount": "2000", "account_number": "X4884", "upi_recipient": "SMAAASH", "txn_ref": "************"}, "actual": [{"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Outflow", "amount": "2000", "date": "30-04-2024", "account_number": "X4884", "txn_ref": "************", "currency": "INR", "upi_recipient": "SMAAASH"}]}, {"test_number": 2, "name": "SBI UPI Credit - Standard Format", "status": "PASS", "expected": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Inflow", "amount": "6000", "account_number": "X4884", "txn_ref": "************"}, "actual": [{"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Inflow", "amount": "6000", "date": "01-05-2024", "account_number": "X4884", "bank_name": "SBI", "txn_ref": "************", "currency": "INR"}]}, {"test_number": 3, "name": "UPI with Person Name", "status": "PASS", "expected": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Outflow", "amount": "180", "account_number": "X4884", "upi_recipient": "<PERSON><PERSON><PERSON>", "txn_ref": "************"}, "actual": [{"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Outflow", "amount": "180", "date": "02-05-2024", "account_number": "X4884", "txn_ref": "************", "currency": "INR", "upi_recipient": "<PERSON><PERSON><PERSON>"}]}, {"test_number": 4, "name": "SBI Debit Card Transaction", "status": "PASS", "expected": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "6026", "card_number": "X3804", "txn_ref": "************"}, "actual": [{"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "6026", "date": "01-05-2024", "account_number": "X3804", "bank_name": "SBI", "txn_ref": "************", "currency": "INR", "card_number": "X3804", "merchant_name": "********"}, {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "32925.97", "currency": "INR"}]}, {"test_number": 5, "name": "Credit Card Payment", "status": "PASS", "expected": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Inflow", "amount": "13293"}, "actual": [{"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Inflow", "amount": "13293", "bank_name": "SBI", "currency": "INR"}, {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "68963.54", "currency": "INR"}]}, {"test_number": 6, "name": "EMI Conversion", "status": "FAIL", "error": "Field 'sms_type': expected 'Payment', got 'Purchase'", "expected": {"sms_type": "Payment", "sms_event_subtype": "EMI Payment", "sms_info_type": "Outflow", "emi_amount": "36866.65", "merchant_name": "VIVO Mobiles"}, "actual": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "36866.65", "date": "01-05-2024", "currency": "INR", "merchant_name": "VIVO Mobiles has been converted to 6 EMIs at 15 percent reducing rate of interest and Processing Fee of Rs"}}, {"test_number": 7, "name": "<PERSON>an <PERSON>", "status": "FAIL", "error": "Field 'sms_event_subtype': expected 'Loan', got 'Bank Account'", "expected": {"sms_type": "Accounts", "sms_event_subtype": "Loan", "sms_info_type": "Account Status", "account_number": "****1772", "current_amount": "2661", "default_status": "overdue"}, "actual": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "2661", "account_number": "****1772", "bank_name": "RBL Bank", "currency": "INR"}}, {"test_number": 8, "name": "Cash Payment for Loan", "status": "PASS", "expected": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Inflow", "amount": "2670", "account_number": "XX1772", "txn_ref": "********************"}, "actual": [{"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Inflow", "amount": "2670", "date": "30-04-2020", "account_number": "XX1772", "txn_ref": "********************", "currency": "INR"}]}, {"test_number": 9, "name": "Balance Update with Transaction", "status": "FAIL", "error": "Result 1: Missing field 'upi_recipient'", "expected": [{"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Outflow", "amount": "180", "account_number": "X4884", "upi_recipient": "<PERSON><PERSON><PERSON>", "txn_ref": "************"}, {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "current_amount": "2000"}], "actual": [{"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Outflow", "amount": "180", "date": "02-05-2024", "account_number": "X4884", "currency": "INR"}, {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "2000", "currency": "INR", "current_amount": "2000"}]}, {"test_number": 10, "name": "Salary Credit", "status": "FAIL", "error": "Missing field 'employer_name'", "expected": {"sms_type": "Deposit & Withdrawal", "sms_event_subtype": "Monthly Salary Credit", "sms_info_type": "Inflow", "amount": "500", "account_number": "X4884", "employer_name": "<PERSON>", "txn_ref": "************"}, "actual": {"sms_type": "Deposit & Withdrawal", "sms_event_subtype": "Monthly Salary Credit", "sms_info_type": "Inflow", "amount": "500", "date": "12-03-2025", "account_number": "X4884", "bank_name": "SBI", "txn_ref": "************", "currency": "INR", "is_salary": false}}, {"test_number": 11, "name": "Investment Transaction", "status": "PASS", "expected": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "amount": "5000", "bank_name": "HDFC"}, "actual": [{"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "amount": "5000", "date": "01-06-2020", "bank_name": "HDFC", "currency": "INR"}]}, {"test_number": 12, "name": "Multiple Amounts in SMS", "status": "FAIL", "error": "Missing field 'interest_charged'", "expected": {"sms_type": "Payment", "sms_event_subtype": "EMI Payment", "sms_info_type": "Outflow", "emi_amount": "5500", "account_number": "123456", "interest_charged": "2100", "late_fee_charged": "0", "txn_ref": "EMI789012"}, "actual": {"sms_type": "Payment", "sms_event_subtype": "EMI Payment", "sms_info_type": "Outflow", "amount": "5500", "date": "01-06-2024", "account_number": "123456", "currency": "INR", "emi_amount": "5500", "is_loan_repayment": true, "is_loan_delayed": false}}, {"test_number": 13, "name": "Different Date Formats", "status": "FAIL", "error": "Missing field 'card_number'", "expected": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "119", "merchant_name": "SPOTIFY SI", "card_number": "4465"}, "actual": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "119", "date": "02-06-2025", "currency": "INR", "merchant_name": "SPOTIFY SI"}}, {"test_number": 14, "name": "NEFT Credit", "status": "FAIL", "error": "Missing field 'employer_name'", "expected": {"sms_type": "Deposit & Withdrawal", "sms_event_subtype": "Monthly Salary Credit", "sms_info_type": "Inflow", "amount": "1494.23", "account_number": "XX4884", "employer_name": "PAYPAL PAYMENTS PL-OPGSP COLL AC", "txn_ref": "CITIN24477998164"}, "actual": {"sms_type": "Deposit & Withdrawal", "sms_event_subtype": "Monthly Salary Credit", "sms_info_type": "Inflow", "amount": "1494.23", "date": "03-06-2024", "account_number": "XX4884", "txn_ref": "CITIN24477998164", "currency": "INR", "is_salary": false}}], "success_rate": 50.0}