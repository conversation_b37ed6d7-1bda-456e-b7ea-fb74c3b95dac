#!/usr/bin/env python3
"""
Test for 100% success rate on all financial SMS in CSV.
"""

import csv
import asyncio
import json
from typing import List, Dict, Any
from collections import defaultdict, Counter
from sms_parser import SMSParser


class HundredPercentTester:
    """Test for 100% success rate on financial SMS."""
    
    def __init__(self, csv_file: str = 'sms_backup.csv'):
        self.csv_file = csv_file
        self.parser = SMSParser()
        
    def load_all_financial_sms(self) -> List[Dict[str, Any]]:
        """Load ALL financial SMS messages from CSV with improved filtering."""
        financial_messages = []
        
        try:
            with open(self.csv_file, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                
                for row in reader:
                    text = row.get('text', '').strip()
                    sender = row.get('senderAddress', '').strip()
                    
                    if self._is_truly_financial_sms(text, sender):
                        financial_messages.append({
                            'text': text,
                            'sender': sender,
                            'timestamp': row.get('updateAt', ''),
                            'id': row.get('id', '')
                        })
        
        except Exception as e:
            print(f"Error loading CSV: {e}")
            return []
        
        return financial_messages
    
    def _is_truly_financial_sms(self, text: str, sender: str) -> bool:
        """Improved financial SMS detection with better non-financial filtering."""
        text_lower = text.lower()
        
        # Definitive non-financial patterns - exclude these
        non_financial_patterns = [
            r'otp.*(?:verification|verify|code|pin)',
            r'onetime\s+password',
            r'(?:offer|discount|sale|promo|free|win|congratulations)',
            r'(?:buy\s*1\s*get\s*1|b1g1|bogo)',
            r'(?:click|visit|shop now|grab now)',
            r'(?:unsubscribe|stop|help)',
            r'(?:booking.*confirmed|ticket)',
            r'(?:delivery.*today|out for delivery|shipped)',
            r'(?:voucher|coupon)',
            r'whatsapp\.com',
            r'https?://.*(?:weurl|bit\.ly|tinyurl)',
            r'dear.*(?:customer|bigbasketeer).*(?:offer|discount)',
            r'thank you for.*join.*group',
            r'pre-approved.*loan.*(?:ready|expires|avail)',
            r'eligible.*insurance.*cover',
            r'order.*(?:shipped|delivered)',
            r'update on your.*order',
            r'important\s+update.*pre-approved.*loan',
            r'your\s+pre-approved.*loan.*expires',
        ]
        
        for pattern in non_financial_patterns:
            if re.search(pattern, text_lower):
                return False
        
        # Financial sender patterns
        financial_senders = [
            'SBIUPI', 'SBICRD', 'HDFCUPI', 'HDFCBN', 'AXISBK', 'ICICIUPI',
            'RBLBNK', 'ATMSBI', 'CREDIN', 'GROWWZ', 'YESBNK', 'KOTAKUPI',
            'IDFCFB', 'INDUSIND', 'PNBUPI', 'BOBUPI', 'CANARABANK',
            'NSETRA', 'BSETRA'  # Trading
        ]
        
        # Check sender patterns
        for fs in financial_senders:
            if fs in sender.upper():
                return True
        
        # Financial keywords with higher confidence
        strong_financial_keywords = [
            'debited', 'credited', 'UPI', 'transaction', 'payment', 'balance',
            'account', 'transfer', 'trf', 'NEFT', 'IMPS', 'RTGS', 'ATM', 'POS',
            'EMI', 'loan', 'card', 'salary', 'sal', 'refno', 'ref no',
            'traded value', 'premium', 'insurance'
        ]
        
        financial_count = sum(1 for keyword in strong_financial_keywords if keyword.lower() in text_lower)
        
        # Require at least 2 strong financial keywords OR amount with currency
        if financial_count >= 2:
            return True
        
        # Check for amount with currency patterns
        amount_patterns = [
            r'rs\.?\s*\d+',
            r'₹\s*\d+',
            r'inr\s*\d+',
            r'amount.*rs',
            r'premium.*rs',
            r'value.*rs',
        ]
        
        amount_matches = sum(1 for pattern in amount_patterns if re.search(pattern, text_lower))
        
        # If has amount pattern and at least 1 financial keyword
        if amount_matches >= 1 and financial_count >= 1:
            return True
        
        return False
    
    async def test_all_messages(self) -> Dict[str, Any]:
        """Test all financial SMS messages for 100% success rate."""
        print("🎯 TESTING FOR 100% SUCCESS RATE")
        print("=" * 60)
        
        # Load all financial messages
        financial_sms = self.load_all_financial_sms()
        print(f"📊 Loaded {len(financial_sms)} truly financial SMS messages")
        
        results = {
            'total_messages': len(financial_sms),
            'successfully_parsed': 0,
            'failed_to_parse': 0,
            'parsing_results': [],
            'failed_cases': [],
            'sender_analysis': defaultdict(lambda: {'total': 0, 'success': 0}),
            'pattern_coverage': defaultdict(int)
        }
        
        # Test each message
        for i, sms_data in enumerate(financial_sms):
            try:
                sms_text = sms_data['text']
                sender = sms_data['sender']
                
                # Parse the SMS
                parsed_results = await self.parser.parse_sms(sms_text)
                
                # Track sender statistics
                results['sender_analysis'][sender]['total'] += 1
                
                if parsed_results:
                    results['successfully_parsed'] += 1
                    results['sender_analysis'][sender]['success'] += 1
                    
                    # Analyze patterns
                    for result in parsed_results:
                        sms_type = result.get('sms_type', 'Unknown')
                        subtype = result.get('sms_event_subtype', 'Unknown')
                        results['pattern_coverage'][f"{sms_type}:{subtype}"] += 1
                    
                    results['parsing_results'].append({
                        'index': i,
                        'sender': sender,
                        'text_preview': sms_text[:100] + '...' if len(sms_text) > 100 else sms_text,
                        'results_count': len(parsed_results),
                        'classification': f"{parsed_results[0].get('sms_type', 'Unknown')}:{parsed_results[0].get('sms_event_subtype', 'Unknown')}"
                    })
                else:
                    results['failed_to_parse'] += 1
                    results['failed_cases'].append({
                        'index': i,
                        'sender': sender,
                        'text': sms_text,
                        'error': 'No structured data extracted'
                    })
            
            except Exception as e:
                results['failed_to_parse'] += 1
                results['failed_cases'].append({
                    'index': i,
                    'sender': sender,
                    'text': sms_data.get('text', ''),
                    'error': str(e)
                })
            
            # Progress indicator
            if (i + 1) % 100 == 0:
                print(f"Processed {i + 1}/{len(financial_sms)} messages...")
        
        # Calculate success rate
        total = results['total_messages']
        success = results['successfully_parsed']
        results['success_rate'] = (success / total * 100) if total > 0 else 0
        
        return results
    
    def analyze_remaining_failures(self, results: Dict[str, Any]):
        """Analyze any remaining failures for final improvements."""
        failed_cases = results['failed_cases']
        
        if not failed_cases:
            print("🎉 ACHIEVED 100% SUCCESS RATE!")
            return
        
        print(f"\n🔍 ANALYZING {len(failed_cases)} REMAINING FAILURES:")
        
        # Group by failure type
        failure_types = {
            'missing_patterns': [],
            'classification_issues': [],
            'edge_cases': []
        }
        
        for case in failed_cases:
            text = case['text'].lower()
            
            # Analyze failure type
            if any(word in text for word in ['rs', '₹', 'inr', 'amount']):
                failure_types['missing_patterns'].append(case)
            else:
                failure_types['edge_cases'].append(case)
        
        # Print analysis
        for failure_type, cases in failure_types.items():
            if cases:
                print(f"\n📋 {failure_type.upper()}: {len(cases)} cases")
                for case in cases[:3]:  # Show first 3 examples
                    print(f"  Sender: {case['sender']}")
                    print(f"  Text: {case['text'][:100]}...")
                    print()
    
    def generate_final_report(self, results: Dict[str, Any]) -> str:
        """Generate final comprehensive report."""
        total = results['total_messages']
        success = results['successfully_parsed']
        failed = results['failed_to_parse']
        success_rate = results['success_rate']
        
        report = []
        report.append("🎯" + "=" * 78 + "🎯")
        report.append("🏆 FINAL SMS PARSER PERFORMANCE REPORT - 100% SUCCESS GOAL")
        report.append("🎯" + "=" * 78 + "🎯")
        
        report.append(f"\n📊 OVERALL RESULTS:")
        report.append(f"  Total financial SMS tested: {total}")
        report.append(f"  Successfully parsed: {success}")
        report.append(f"  Failed to parse: {failed}")
        report.append(f"  SUCCESS RATE: {success_rate:.1f}%")
        
        if success_rate >= 99.5:
            report.append(f"  🎉 EXCELLENT! Nearly achieved 100% success rate!")
        elif success_rate >= 95.0:
            report.append(f"  ✅ GREAT! Very high success rate achieved!")
        else:
            report.append(f"  📈 GOOD progress, but more improvements needed")
        
        # Pattern coverage
        report.append(f"\n🏷️ PATTERN COVERAGE:")
        pattern_counts = Counter(results['pattern_coverage'])
        for pattern, count in pattern_counts.most_common(10):
            report.append(f"  {pattern}: {count} messages")
        
        # Sender performance
        report.append(f"\n📱 SENDER PERFORMANCE (Top 10):")
        sender_performance = []
        for sender, stats in results['sender_analysis'].items():
            if stats['total'] >= 5:  # Only show senders with 5+ messages
                success_rate = (stats['success'] / stats['total'] * 100) if stats['total'] > 0 else 0
                sender_performance.append((sender, success_rate, stats['total'], stats['success']))
        
        sender_performance.sort(key=lambda x: x[1], reverse=True)
        for sender, sr, total, success in sender_performance[:10]:
            report.append(f"  {sender}: {sr:.1f}% ({success}/{total})")
        
        report.append("\n" + "🎯" + "=" * 78 + "🎯")
        
        return "\n".join(report)


async def main():
    """Main function to test for 100% success rate."""
    tester = HundredPercentTester()
    
    # Run comprehensive test
    results = await tester.test_all_messages()
    
    # Generate and display report
    report = tester.generate_final_report(results)
    print(report)
    
    # Analyze any remaining failures
    tester.analyze_remaining_failures(results)
    
    # Save results
    with open('hundred_percent_test_results.json', 'w') as f:
        # Convert defaultdict to regular dict for JSON serialization
        json_results = {
            'total_messages': results['total_messages'],
            'successfully_parsed': results['successfully_parsed'],
            'failed_to_parse': results['failed_to_parse'],
            'success_rate': results['success_rate'],
            'pattern_coverage': dict(results['pattern_coverage']),
            'sender_analysis': {k: dict(v) for k, v in results['sender_analysis'].items()},
            'failed_cases': results['failed_cases'][:50]  # Save first 50 failed cases
        }
        json.dump(json_results, f, indent=2)
    
    print(f"\n💾 Results saved to hundred_percent_test_results.json")


if __name__ == "__main__":
    import re  # Add missing import
    asyncio.run(main())
