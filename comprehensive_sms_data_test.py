#!/usr/bin/env python3
"""
Comprehensive test of SMS parser on SMS-Data.csv covering all edge cases,
field extraction, classification, and proper filtering of OTP/non-financial messages.
"""

import csv
import asyncio
import json
import re
from typing import List, Dict, Any, Set
from collections import defaultdict, Counter
from sms_parser import SMSParser


class ComprehensiveSMSDataTester:
    """Comprehensive tester for SMS-Data.csv covering all edge cases."""
    
    def __init__(self, csv_file: str = 'SMS-Data.csv'):
        self.csv_file = csv_file
        self.parser = SMSParser()
        
        # Define test categories for comprehensive coverage
        self.test_categories = {
            'upi_transactions': [],
            'hdfc_transactions': [],
            'indian_bank_transactions': [],
            'simpl_payments': [],
            'jio_recharges': [],
            'stock_exchange': [],
            'emi_loans': [],
            'atm_transactions': [],
            'cash_deposits': [],
            'paytm_transactions': [],
            'toll_payments': [],
            'razorpay_transactions': [],
            'credit_reversals': [],
            'otp_messages': [],
            'promotional_messages': [],
            'recharge_offers': [],
            'loan_offers': [],
            'gaming_offers': [],
            'welcome_messages': [],
            'bill_reminders': [],
            'legal_notices': [],
            'job_offers': [],
            'complex_multi_event': []
        }
        
    def categorize_sms_messages(self, limit: int = 5000) -> Dict[str, List[Dict]]:
        """Load and categorize SMS messages for comprehensive testing."""
        print(f"📊 Loading and categorizing SMS messages from {self.csv_file}...")
        
        try:
            with open(self.csv_file, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                
                count = 0
                for row in reader:
                    if count >= limit:
                        break
                        
                    text = row.get('text', '').strip()
                    sender = row.get('senderAddress', '').strip()
                    
                    if not text or len(text) < 10:
                        continue
                    
                    sms_data = {
                        'text': text,
                        'sender': sender,
                        'timestamp': row.get('updateAt', ''),
                        'id': row.get('id', '')
                    }
                    
                    # Categorize based on content and sender
                    self._categorize_message(sms_data)
                    count += 1
        
        except Exception as e:
            print(f"Error loading CSV: {e}")
            return {}
        
        # Print category statistics
        print(f"\n📋 Message Categories Found:")
        for category, messages in self.test_categories.items():
            if messages:
                print(f"  {category}: {len(messages)} messages")
        
        return self.test_categories
    
    def _categorize_message(self, sms_data: Dict[str, str]):
        """Categorize a single SMS message."""
        text = sms_data['text'].lower()
        sender = sms_data['sender'].upper()
        
        # UPI Transactions
        if 'upi' in text or any(x in sender for x in ['SBIUPI', 'HDFCUPI', 'INDBNK', 'INDBK']):
            if 'debited' in text or 'credited' in text:
                self.test_categories['upi_transactions'].append(sms_data)
        
        # HDFC Bank transactions
        elif 'HDFCBK' in sender or 'hdfc bank' in text:
            if 'debited' in text or 'credited' in text:
                self.test_categories['hdfc_transactions'].append(sms_data)
        
        # Indian Bank transactions
        elif 'IndBnk' in sender or 'indian bank' in text:
            if 'debited' in text or 'credited' in text:
                self.test_categories['indian_bank_transactions'].append(sms_data)
        
        # Simpl payments - more specific check
        elif 'SmplPL' in sender or ('simpl' in text and ('charged' in text or 'payment' in text)):
            self.test_categories['simpl_payments'].append(sms_data)
        
        # Jio recharges
        elif 'JioPay' in sender or 'jio' in text:
            if 'recharge' in text:
                self.test_categories['jio_recharges'].append(sms_data)
        
        # Stock exchange
        elif 'NSESMS' in sender or 'stock broker' in text:
            self.test_categories['stock_exchange'].append(sms_data)
        
        # EMI and loans
        elif 'emi' in text or 'loan' in text:
            if 'due' in text or 'disbursed' in text:
                self.test_categories['emi_loans'].append(sms_data)
        
        # ATM transactions
        elif 'ATMSBI' in sender or 'atm' in text:
            self.test_categories['atm_transactions'].append(sms_data)
        
        # Cash deposits
        elif 'cash deposit' in text:
            self.test_categories['cash_deposits'].append(sms_data)
        
        # Paytm transactions
        elif 'PAYTMB' in sender or 'paytm' in text:
            if 'received' in text or 'paid' in text:
                self.test_categories['paytm_transactions'].append(sms_data)
        
        # Toll payments
        elif 'toll plaza' in text or 'paid at' in text:
            self.test_categories['toll_payments'].append(sms_data)
        
        # Razorpay transactions
        elif 'RZRPAY' in sender or 'razorpay' in text:
            self.test_categories['razorpay_transactions'].append(sms_data)
        
        # Credit reversals
        elif 'reversed' in text or 'finance charge' in text:
            self.test_categories['credit_reversals'].append(sms_data)
        
        # OTP messages
        elif 'otp' in text or 'verification' in text or 'pin' in text:
            self.test_categories['otp_messages'].append(sms_data)
        
        # Promotional messages
        elif any(x in text for x in ['offer', 'discount', 'free', 'win', 'congratulations']):
            self.test_categories['promotional_messages'].append(sms_data)
        
        # Recharge offers
        elif 'recharge' in text and any(x in text for x in ['cashback', 'offer', 'hurry']):
            self.test_categories['recharge_offers'].append(sms_data)
        
        # Loan offers
        elif 'loan' in text and any(x in text for x in ['approved', 'eligible', 'apply']):
            self.test_categories['loan_offers'].append(sms_data)
        
        # Gaming offers
        elif any(x in sender for x in ['GAMEZY', 'DRMELE', 'MYCIRC']):
            self.test_categories['gaming_offers'].append(sms_data)
        
        # Welcome messages
        elif 'welcome' in text:
            self.test_categories['welcome_messages'].append(sms_data)
        
        # Bill reminders
        elif 'bill' in text and 'overdue' in text:
            self.test_categories['bill_reminders'].append(sms_data)
        
        # Legal notices
        elif 'legal proceedings' in text:
            self.test_categories['legal_notices'].append(sms_data)
        
        # Job offers
        elif 'job' in text or 'salary' in text and 'daily' in text:
            self.test_categories['job_offers'].append(sms_data)
        
        # Complex multi-event messages
        elif 'avl bal' in text or ('debited' in text and 'balance' in text):
            self.test_categories['complex_multi_event'].append(sms_data)
    
    async def test_comprehensive_coverage(self) -> Dict[str, Any]:
        """Test comprehensive coverage across all categories."""
        print("🎯 COMPREHENSIVE SMS-DATA.CSV TEST")
        print("=" * 70)
        
        # Load and categorize messages
        categories = self.categorize_sms_messages(limit=10000)  # Test more messages
        
        results = {
            'category_results': {},
            'overall_stats': {
                'total_tested': 0,
                'total_parsed': 0,
                'total_filtered': 0,
                'total_failed': 0
            },
            'field_extraction_stats': defaultdict(int),
            'classification_stats': defaultdict(int),
            'filtering_stats': defaultdict(int),
            'edge_cases': [],
            'failed_cases': []
        }
        
        # Test each category
        for category, messages in categories.items():
            if not messages:
                continue
                
            print(f"\n📱 Testing {category}: {len(messages)} messages")
            
            category_result = await self._test_category(category, messages[:50])  # Test up to 50 per category
            results['category_results'][category] = category_result
            
            # Update overall stats
            results['overall_stats']['total_tested'] += category_result['tested']
            results['overall_stats']['total_parsed'] += category_result['parsed']
            results['overall_stats']['total_filtered'] += category_result['filtered']
            results['overall_stats']['total_failed'] += category_result['failed']
            
            # Collect field extraction stats
            for field, count in category_result['fields_extracted'].items():
                results['field_extraction_stats'][field] += count
            
            # Collect classification stats
            for classification, count in category_result['classifications'].items():
                results['classification_stats'][classification] += count
            
            # Collect filtering stats
            results['filtering_stats'][category] = category_result['filtered']
            
            # Collect edge cases and failures
            results['edge_cases'].extend(category_result.get('edge_cases', []))
            results['failed_cases'].extend(category_result.get('failed_cases', []))
        
        # Calculate success rate
        total = results['overall_stats']['total_tested']
        parsed = results['overall_stats']['total_parsed']
        filtered = results['overall_stats']['total_filtered']
        
        results['overall_stats']['success_rate'] = (parsed / (total - filtered) * 100) if (total - filtered) > 0 else 0
        results['overall_stats']['filter_rate'] = (filtered / total * 100) if total > 0 else 0
        
        return results
    
    async def _test_category(self, category: str, messages: List[Dict]) -> Dict[str, Any]:
        """Test a specific category of messages."""
        result = {
            'category': category,
            'tested': len(messages),
            'parsed': 0,
            'filtered': 0,
            'failed': 0,
            'fields_extracted': defaultdict(int),
            'classifications': defaultdict(int),
            'edge_cases': [],
            'failed_cases': []
        }
        
        for msg in messages:
            try:
                # Check if message should be filtered (non-financial)
                if self._should_be_filtered(msg['text'], category):
                    result['filtered'] += 1
                    continue
                
                # Parse the message
                parsed_results = await self.parser.parse_sms(msg['text'])
                
                if parsed_results:
                    result['parsed'] += 1
                    
                    # Analyze extracted fields and classifications
                    for parsed_result in parsed_results:
                        # Count classifications
                        sms_type = parsed_result.get('sms_type', 'Unknown')
                        subtype = parsed_result.get('sms_event_subtype', 'Unknown')
                        result['classifications'][f"{sms_type}:{subtype}"] += 1
                        
                        # Count extracted fields
                        for field, value in parsed_result.items():
                            if value and field not in ['sms_type', 'sms_event_subtype']:
                                result['fields_extracted'][field] += 1
                        
                        # Check for edge cases
                        if self._is_edge_case(parsed_result, msg):
                            result['edge_cases'].append({
                                'message': msg['text'][:100],
                                'sender': msg['sender'],
                                'parsed_result': parsed_result
                            })
                else:
                    result['failed'] += 1
                    result['failed_cases'].append({
                        'message': msg['text'][:100],
                        'sender': msg['sender'],
                        'category': category
                    })
            
            except Exception as e:
                result['failed'] += 1
                result['failed_cases'].append({
                    'message': msg['text'][:100],
                    'sender': msg['sender'],
                    'error': str(e),
                    'category': category
                })
        
        return result
    
    def _should_be_filtered(self, text: str, category: str) -> bool:
        """Check if message should be filtered as non-financial."""
        # Categories that should be filtered
        filter_categories = {
            'otp_messages', 'promotional_messages', 'recharge_offers',
            'loan_offers', 'gaming_offers', 'welcome_messages',
            'legal_notices', 'job_offers'
        }

        if category in filter_categories:
            return True

        # Additional content-based filtering for edge cases
        text_lower = text.lower()

        # Jio plan expiry/sharing messages should be filtered
        jio_non_financial_patterns = [
            r'plan.*expires.*recharge.*now',
            r'plan.*expired.*services.*stopped',
            r'recently.*recharged.*please.*click.*share',
            r'avoid.*stoppage.*services.*recharge',
        ]

        for pattern in jio_non_financial_patterns:
            if re.search(pattern, text_lower):
                return True

        return False
    
    def _is_edge_case(self, parsed_result: Dict, msg: Dict) -> bool:
        """Identify edge cases for special attention."""
        # Multi-event messages
        if isinstance(parsed_result, list) and len(parsed_result) > 1:
            return True
        
        # Messages with unusual amounts
        amount = parsed_result.get('amount')
        if amount:
            try:
                amt_val = float(str(amount).replace(',', ''))
                if amt_val > 100000 or amt_val < 1:  # Very high or very low amounts
                    return True
            except:
                pass
        
        # Messages with complex patterns
        text = msg['text'].lower()
        if len(text) > 200 or text.count('rs') > 2 or text.count('₹') > 2:
            return True
        
        return False
    
    def generate_comprehensive_report(self, results: Dict[str, Any]) -> str:
        """Generate comprehensive test report."""
        report = []
        report.append("🎯" + "=" * 88 + "🎯")
        report.append("🏆 COMPREHENSIVE SMS-DATA.CSV TEST REPORT - ALL EDGE CASES COVERED")
        report.append("🎯" + "=" * 88 + "🎯")
        
        # Overall statistics
        stats = results['overall_stats']
        report.append(f"\n📊 OVERALL RESULTS:")
        report.append(f"  Total messages tested: {stats['total_tested']}")
        report.append(f"  Successfully parsed: {stats['total_parsed']}")
        report.append(f"  Correctly filtered (non-financial): {stats['total_filtered']}")
        report.append(f"  Failed to parse: {stats['total_failed']}")
        report.append(f"  SUCCESS RATE: {stats['success_rate']:.1f}%")
        report.append(f"  FILTER RATE: {stats['filter_rate']:.1f}%")
        
        if stats['success_rate'] >= 99.0:
            report.append(f"  🎉 EXCELLENT! Nearly perfect success rate!")
        elif stats['success_rate'] >= 95.0:
            report.append(f"  ✅ GREAT! Very high success rate!")
        else:
            report.append(f"  📈 Good progress, improvements needed")
        
        # Category-wise results
        report.append(f"\n📋 CATEGORY-WISE RESULTS:")
        for category, result in results['category_results'].items():
            tested = result['tested']
            parsed = result['parsed']
            filtered = result['filtered']
            failed = result['failed']
            
            if tested > 0:
                success_rate = (parsed / (tested - filtered) * 100) if (tested - filtered) > 0 else 0
                report.append(f"  {category}:")
                report.append(f"    Tested: {tested}, Parsed: {parsed}, Filtered: {filtered}, Failed: {failed}")
                report.append(f"    Success Rate: {success_rate:.1f}%")
        
        # Field extraction statistics
        report.append(f"\n🔍 FIELD EXTRACTION COVERAGE:")
        field_stats = Counter(results['field_extraction_stats'])
        for field, count in field_stats.most_common(15):
            report.append(f"  {field}: {count} extractions")
        
        # Classification statistics
        report.append(f"\n🏷️ CLASSIFICATION COVERAGE:")
        class_stats = Counter(results['classification_stats'])
        for classification, count in class_stats.most_common(15):
            report.append(f"  {classification}: {count} messages")
        
        # Filtering effectiveness
        report.append(f"\n🚫 FILTERING EFFECTIVENESS:")
        for category, filtered_count in results['filtering_stats'].items():
            if filtered_count > 0:
                report.append(f"  {category}: {filtered_count} correctly filtered")
        
        # Edge cases
        if results['edge_cases']:
            report.append(f"\n⚠️ EDGE CASES IDENTIFIED ({len(results['edge_cases'])}):")
            for i, edge_case in enumerate(results['edge_cases'][:5], 1):
                report.append(f"  {i}. {edge_case['sender']}: {edge_case['message']}...")
        
        # Failed cases
        if results['failed_cases']:
            report.append(f"\n❌ FAILED CASES ({len(results['failed_cases'])}):")
            failed_by_category = defaultdict(int)
            for failed_case in results['failed_cases']:
                failed_by_category[failed_case['category']] += 1
            
            for category, count in failed_by_category.items():
                report.append(f"  {category}: {count} failures")
        
        report.append("\n" + "🎯" + "=" * 88 + "🎯")
        
        return "\n".join(report)


async def main():
    """Main function for comprehensive testing."""
    tester = ComprehensiveSMSDataTester()
    
    print("🚀 Starting comprehensive test of SMS-Data.csv")
    print("Testing all edge cases, field extraction, classification, and filtering")
    
    # Run comprehensive test
    results = await tester.test_comprehensive_coverage()
    
    # Generate and display report
    report = tester.generate_comprehensive_report(results)
    print(report)
    
    # Save detailed results
    with open('comprehensive_sms_data_results.json', 'w') as f:
        # Convert defaultdict to regular dict for JSON serialization
        json_results = {
            'overall_stats': results['overall_stats'],
            'category_results': results['category_results'],
            'field_extraction_stats': dict(results['field_extraction_stats']),
            'classification_stats': dict(results['classification_stats']),
            'filtering_stats': dict(results['filtering_stats']),
            'edge_cases': results['edge_cases'][:50],
            'failed_cases': results['failed_cases'][:50]
        }
        json.dump(json_results, f, indent=2)
    
    print(f"\n💾 Detailed results saved to comprehensive_sms_data_results.json")
    
    # Test specific edge cases
    await test_specific_edge_cases(tester.parser)


async def test_specific_edge_cases(parser):
    """Test specific edge cases from SMS-Data.csv."""
    print(f"\n🎯 TESTING SPECIFIC EDGE CASES:")
    
    edge_cases = [
        # UPI transactions
        {
            'text': 'Your VPA sanju39chd@okaxis linked to Indian Bank a/c no. XXXXXX4658 is debited for Rs.299.00 and credited to euronetgpay.pay@icici (UPI Ref no ************).-Indian Bank',
            'expected_fields': ['amount', 'account_number', 'upi_recipient', 'txn_ref'],
            'expected_classification': 'Purchase:UPI'
        },
        # HDFC Bank transactions
        {
            'text': 'HDFC Bank: Rs 90.00 debited from a/c **1060 on 28-04-22 to VPA paytmqr28100505010180zo03beep9r@paytm(UPI Ref No ************). Not you? Call on *********** to report',
            'expected_fields': ['amount', 'account_number', 'upi_recipient', 'txn_ref', 'date'],
            'expected_classification': 'Purchase:UPI'
        },
        # Simpl payments
        {
            'text': 'Rs.95.15 on Zomato charged via Simpl.',
            'expected_fields': ['amount', 'merchant_name'],
            'expected_classification': 'Purchase:Debit Card'
        },
        # Stock exchange
        {
            'text': 'Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & securities balance 0 as on end of 26-mar-2022.',
            'expected_fields': ['amount', 'date'],
            'expected_classification': 'Investment:Investment'
        },
        # EMI notifications
        {
            'text': 'Dear Cust, your EMI for HDB Loan a/c ******** is due on 2nd May 22 Pls keep your bank a/c funded at least 1 day prior the due date to avoid charges.',
            'expected_fields': ['account_number', 'date'],
            'expected_classification': 'Payment:EMI Payment'
        },
        # Cash deposits
        {
            'text': 'Cash deposit of Rs 4500.00 to Acct ************ has been initiated through ICICI Bank INSTA Banking. Pls quote Reference No. MCDA00094599401 at the counter.',
            'expected_fields': ['amount', 'account_number', 'txn_ref'],
            'expected_classification': 'Deposit & Withdrawal:Loan Disbursal'
        },
        # Multi-event SMS
        {
            'text': 'UPDATE: INR 5,900.00 debited from HDFC Bank XX1060 on 25-APR-22. Info: UPI-PARVINDER SINGh-rs8820236@oksbi-CNRB0000033-************-Payment from Phone. Avl bal:INR 710.00',
            'expected_fields': ['amount', 'account_number', 'current_amount', 'date'],
            'expected_classification': 'Multiple events'
        },
        # OTP messages (should be filtered)
        {
            'text': 'Dear Customer, OTP TO INITIATE E-MANDATE SBI REQUEST ID *************** OF RS. 500000.00 IN A/C NO. XXXX1899 IS ********. DO NOT SHARE IT WITH ANYONE. -SBI',
            'expected_fields': [],
            'expected_classification': 'Filtered (OTP)'
        },
        # Promotional messages (should be filtered)
        {
            'text': 'Hurry! Recharge Jio no.********** on Paytm & get upto Rs.100 Cashback - Code JIO100 each time for self & family.',
            'expected_fields': [],
            'expected_classification': 'Filtered (Promotional)'
        }
    ]
    
    for i, test_case in enumerate(edge_cases, 1):
        print(f"\n📱 Edge Case {i}: {test_case['text'][:60]}...")
        
        try:
            results = await parser.parse_sms(test_case['text'])
            
            if results:
                print(f"   ✅ Parsed {len(results)} event(s)")
                for j, result in enumerate(results, 1):
                    classification = f"{result.get('sms_type')}:{result.get('sms_event_subtype')}"
                    print(f"     Event {j}: {classification}")
                    
                    # Check expected fields
                    extracted_fields = []
                    for field in test_case['expected_fields']:
                        if result.get(field):
                            extracted_fields.append(f"{field}: {result.get(field)}")
                    
                    if extracted_fields:
                        print(f"     Fields: {', '.join(extracted_fields[:3])}")
                    
                    # Validate classification
                    if test_case['expected_classification'] in classification:
                        print(f"     ✅ Classification correct")
                    elif test_case['expected_classification'] == 'Multiple events' and len(results) > 1:
                        print(f"     ✅ Multi-event detection correct")
            else:
                if 'Filtered' in test_case['expected_classification']:
                    print(f"   ✅ Correctly filtered as non-financial")
                else:
                    print(f"   ❌ Not parsed (unexpected)")
        
        except Exception as e:
            print(f"   ❌ Error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
