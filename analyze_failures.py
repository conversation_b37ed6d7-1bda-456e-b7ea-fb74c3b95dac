#!/usr/bin/env python3
"""
Analyze failed SMS parsing cases to achieve 100% success rate.
"""

import csv
import re
import asyncio
from typing import List, Dict, Any
from collections import defaultdict, Counter
from sms_parser import SMSParser


class FailureAnalyzer:
    """Analyze failed cases to improve parser to 100% success rate."""
    
    def __init__(self, csv_file: str = 'sms_backup.csv'):
        self.csv_file = csv_file
        self.parser = SMSParser()
        self.failed_cases = []
        self.success_cases = []
        
    def load_financial_sms(self) -> List[Dict[str, Any]]:
        """Load financial SMS messages from CSV."""
        financial_messages = []
        
        try:
            with open(self.csv_file, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                
                for row in reader:
                    text = row.get('text', '').strip()
                    sender = row.get('senderAddress', '').strip()
                    
                    if self._is_financial_sms(text, sender):
                        financial_messages.append({
                            'text': text,
                            'sender': sender,
                            'timestamp': row.get('updateAt', ''),
                            'id': row.get('id', '')
                        })
        
        except Exception as e:
            print(f"Error loading CSV: {e}")
            return []
        
        return financial_messages
    
    def _is_financial_sms(self, text: str, sender: str) -> bool:
        """Determine if SMS is financial."""
        # More comprehensive financial detection
        financial_senders = [
            'SBIUPI', 'SBICRD', 'HDFCUPI', 'HDFCBN', 'AXISBK', 'ICICIUPI',
            'RBLBNK', 'ATMSBI', 'CREDIN', 'GROWWZ', 'YESBNK', 'KOTAKUPI',
            'IDFCFB', 'INDUSIND', 'PNBUPI', 'BOBUPI', 'CANARABANK'
        ]
        
        # Check sender patterns
        for fs in financial_senders:
            if fs in sender.upper():
                return True
        
        # Check content patterns - more comprehensive
        financial_keywords = [
            'debited', 'credited', 'UPI', 'transaction', 'payment', 'balance',
            'Rs', '₹', 'INR', 'refno', 'EMI', 'loan', 'card', 'account',
            'transfer', 'trf', 'NEFT', 'IMPS', 'RTGS', 'ATM', 'POS',
            'merchant', 'bill', 'recharge', 'topup', 'cashback', 'refund',
            'salary', 'sal', 'credit card', 'debit card', 'limit', 'overdue',
            'outstanding', 'due', 'amount', 'txn', 'ref no', 'reference'
        ]
        
        text_lower = text.lower()
        keyword_count = sum(1 for keyword in financial_keywords if keyword.lower() in text_lower)
        
        # Require at least 2 financial keywords or specific amount patterns
        if keyword_count >= 2:
            return True
        
        # Check for amount patterns
        amount_patterns = [
            r'rs\.?\s*\d+',
            r'₹\s*\d+',
            r'inr\s*\d+',
            r'\d+\.\d{2}',
            r'amount.*\d+',
            r'\d+.*rs',
        ]
        
        for pattern in amount_patterns:
            if re.search(pattern, text_lower):
                return True
        
        return False
    
    async def analyze_all_failures(self) -> Dict[str, Any]:
        """Analyze all failed cases to identify missing patterns."""
        print("🔍 Loading and analyzing all financial SMS...")
        financial_sms = self.load_financial_sms()
        print(f"Found {len(financial_sms)} financial SMS messages")
        
        # Test each message
        for i, sms_data in enumerate(financial_sms):
            try:
                results = await self.parser.parse_sms(sms_data['text'])
                
                if results:
                    self.success_cases.append({
                        'sms': sms_data,
                        'results': results
                    })
                else:
                    self.failed_cases.append(sms_data)
                
                if (i + 1) % 100 == 0:
                    print(f"Processed {i + 1}/{len(financial_sms)} messages...")
            
            except Exception as e:
                self.failed_cases.append({
                    **sms_data,
                    'error': str(e)
                })
        
        print(f"\n📊 Analysis Results:")
        print(f"Success: {len(self.success_cases)}")
        print(f"Failed: {len(self.failed_cases)}")
        print(f"Success rate: {len(self.success_cases)/(len(self.success_cases)+len(self.failed_cases))*100:.1f}%")
        
        return await self._analyze_failure_patterns()
    
    async def _analyze_failure_patterns(self) -> Dict[str, Any]:
        """Analyze patterns in failed cases."""
        print(f"\n🔍 Analyzing {len(self.failed_cases)} failed cases...")
        
        analysis = {
            'failed_by_sender': defaultdict(list),
            'common_failure_patterns': [],
            'missing_keywords': Counter(),
            'missing_amount_patterns': [],
            'missing_date_patterns': [],
            'missing_account_patterns': [],
            'non_financial_messages': [],
            'edge_cases': []
        }
        
        for case in self.failed_cases:
            text = case['text']
            sender = case['sender']
            
            analysis['failed_by_sender'][sender].append(text)
            
            # Check if it's actually non-financial
            if self._is_non_financial(text):
                analysis['non_financial_messages'].append(case)
                continue
            
            # Analyze missing patterns
            self._analyze_missing_patterns(text, analysis)
        
        # Print analysis
        self._print_failure_analysis(analysis)
        
        return analysis
    
    def _is_non_financial(self, text: str) -> bool:
        """Check if message is actually non-financial."""
        non_financial_keywords = [
            'offer', 'discount', 'sale', 'promo', 'advertisement', 'ad',
            'congratulations', 'winner', 'prize', 'lucky', 'free',
            'subscribe', 'unsubscribe', 'stop', 'help', 'info',
            'weather', 'news', 'update', 'notification', 'alert',
            'reminder', 'appointment', 'meeting', 'event',
            'birthday', 'anniversary', 'festival', 'holiday',
            'otp', 'verification', 'code', 'pin', 'password'
        ]
        
        text_lower = text.lower()
        non_financial_count = sum(1 for keyword in non_financial_keywords if keyword in text_lower)
        
        # If it has many non-financial keywords and no clear financial indicators
        if non_financial_count >= 2:
            financial_indicators = ['rs', '₹', 'inr', 'debited', 'credited', 'balance', 'account']
            financial_count = sum(1 for indicator in financial_indicators if indicator in text_lower)
            
            if financial_count == 0:
                return True
        
        return False
    
    def _analyze_missing_patterns(self, text: str, analysis: Dict):
        """Analyze what patterns are missing for this text."""
        text_lower = text.lower()
        
        # Look for potential amounts not being captured
        potential_amounts = re.findall(r'\d+(?:\.\d{2})?', text)
        if potential_amounts:
            # Check if any amount pattern would match
            has_currency = any(curr in text_lower for curr in ['rs', '₹', 'inr', 'rupees'])
            if has_currency:
                analysis['missing_amount_patterns'].append(text[:100])
        
        # Look for potential dates
        potential_dates = re.findall(r'\d{1,2}[-/]\d{1,2}[-/]\d{2,4}|\d{1,2}[A-Za-z]{3}\d{2,4}', text)
        if potential_dates:
            analysis['missing_date_patterns'].append(text[:100])
        
        # Look for potential account numbers
        potential_accounts = re.findall(r'[X\*]+\d{3,6}|\d{10,}', text)
        if potential_accounts:
            analysis['missing_account_patterns'].append(text[:100])
        
        # Extract unique words for keyword analysis
        words = re.findall(r'\b\w+\b', text_lower)
        for word in words:
            if len(word) > 3:
                analysis['missing_keywords'][word] += 1
    
    def _print_failure_analysis(self, analysis: Dict):
        """Print detailed failure analysis."""
        print(f"\n📋 FAILURE ANALYSIS REPORT")
        print("=" * 60)
        
        # Failed by sender
        print(f"\n📱 TOP FAILING SENDERS:")
        sender_failures = Counter({k: len(v) for k, v in analysis['failed_by_sender'].items()})
        for sender, count in sender_failures.most_common(15):
            print(f"  {sender}: {count} failures")
        
        # Non-financial messages
        print(f"\n🚫 NON-FINANCIAL MESSAGES: {len(analysis['non_financial_messages'])}")
        for case in analysis['non_financial_messages'][:5]:
            print(f"  {case['sender']}: {case['text'][:80]}...")
        
        # Missing patterns
        print(f"\n💰 MISSING AMOUNT PATTERNS: {len(analysis['missing_amount_patterns'])}")
        for pattern in analysis['missing_amount_patterns'][:5]:
            print(f"  {pattern}...")
        
        print(f"\n📅 MISSING DATE PATTERNS: {len(analysis['missing_date_patterns'])}")
        for pattern in analysis['missing_date_patterns'][:5]:
            print(f"  {pattern}...")
        
        print(f"\n🏦 MISSING ACCOUNT PATTERNS: {len(analysis['missing_account_patterns'])}")
        for pattern in analysis['missing_account_patterns'][:5]:
            print(f"  {pattern}...")
        
        # Common words in failed messages
        print(f"\n🔤 COMMON WORDS IN FAILED MESSAGES:")
        for word, count in analysis['missing_keywords'].most_common(20):
            print(f"  {word}: {count}")
    
    def generate_improvement_suggestions(self, analysis: Dict) -> List[str]:
        """Generate specific improvement suggestions."""
        suggestions = []
        
        # Analyze failed senders
        top_failing_senders = Counter({k: len(v) for k, v in analysis['failed_by_sender'].items()})
        
        for sender, count in top_failing_senders.most_common(10):
            if count >= 5:  # Focus on senders with multiple failures
                sample_texts = analysis['failed_by_sender'][sender][:3]
                suggestions.append(f"Add patterns for {sender} (fails {count} times)")
                for text in sample_texts:
                    suggestions.append(f"  Example: {text[:100]}...")
        
        # Suggest new classification rules
        common_words = analysis['missing_keywords'].most_common(30)
        financial_words = [word for word, count in common_words 
                          if count >= 3 and word not in ['the', 'and', 'for', 'you', 'your', 'has', 'been']]
        
        if financial_words:
            suggestions.append("Add new financial keywords:")
            suggestions.extend([f"  - {word}" for word in financial_words[:10]])
        
        return suggestions


async def main():
    """Main function to analyze failures and suggest improvements."""
    analyzer = FailureAnalyzer()
    
    print("🎯 GOAL: ACHIEVE 100% SUCCESS RATE ON ALL CSV SMS")
    print("=" * 60)
    
    analysis = await analyzer.analyze_all_failures()
    
    print(f"\n💡 IMPROVEMENT SUGGESTIONS:")
    suggestions = analyzer.generate_improvement_suggestions(analysis)
    for suggestion in suggestions[:20]:  # Show top 20 suggestions
        print(suggestion)
    
    # Save detailed analysis
    import json
    with open('failure_analysis.json', 'w') as f:
        # Convert defaultdict to regular dict for JSON serialization
        analysis_json = {
            'failed_by_sender': dict(analysis['failed_by_sender']),
            'missing_keywords': dict(analysis['missing_keywords']),
            'missing_amount_patterns': analysis['missing_amount_patterns'][:50],
            'missing_date_patterns': analysis['missing_date_patterns'][:50],
            'missing_account_patterns': analysis['missing_account_patterns'][:50],
            'non_financial_count': len(analysis['non_financial_messages']),
            'total_failed': len(analyzer.failed_cases),
            'total_success': len(analyzer.success_cases)
        }
        json.dump(analysis_json, f, indent=2)
    
    print(f"\n💾 Detailed analysis saved to failure_analysis.json")


if __name__ == "__main__":
    asyncio.run(main())
