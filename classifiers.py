import re
from typing import Dict, List, Tuple
from utils import clean_text


class SMSClassifier:
    """
    SMS classification logic to determine sms_type, sms_event_subtype, and sms_info_type.
    Uses keyword-based classification with priority rules.
    """
    
    def __init__(self):
        self._setup_classification_rules()
    
    def _setup_classification_rules(self):
        """Setup classification rules with keywords and patterns."""
        
        # Purchase classification rules - improved based on CSV analysis
        self.purchase_rules = {
            'UPI': {
                'keywords': ['upi', 'unified payment', 'bhim', 'paytm', 'phonepe', 'gpay', 'google pay', 'sbiupi', 'hdfcupi'],
                'patterns': [
                    r'upi\s+(?:txn|transaction|payment|user)',
                    r'paid\s+(?:via|through|using)\s+upi',
                    r'upi\s+ref',
                    r'dear\s+upi\s+user',  # Dear UPI user
                    r'sbi\s+upi\s+user',   # Dear SBI UPI User
                    r'trf\s+to',           # trf to (transfer to)
                ]
            },
            'Debit Card': {
                'keywords': ['debit card', 'card', 'pos', 'atm', 'merchant'],
                'patterns': [
                    r'card\s+(?:txn|transaction)',
                    r'debit\s+card',
                    r'pos\s+(?:txn|transaction)',
                    r'spent\s+at\s+\w+',
                    r'(?:debit|credit)\s+card\s+[X\*]*\d{4}',  # Debit Card XX1234
                    r'done at\s+\w+',      # done at merchant
                    r'transaction.*at\s+\w+',  # transaction at merchant
                ]
            }
        }
        
        # Payment classification rules - improved EMI detection
        self.payment_rules = {
            'EMI Payment': {
                'keywords': ['emi', 'installment', 'monthly payment', 'loan payment', 'repayment', 'emis'],
                'patterns': [
                    r'emi\s+(?:paid|payment|of|booking)',
                    r'installment\s+(?:paid|payment)',
                    r'loan\s+(?:emi|payment|repayment)',
                    r'converted\s+to\s+\d+\s+emis',  # converted to 6 EMIs
                    r'emi\s+booking\s+amount',       # EMI Booking amount
                    r'\d+\s+emis\s+at',             # 6 EMIs at
                ]
            }
        }
        
        # Deposit & Withdrawal classification rules - improved
        self.deposit_withdrawal_rules = {
            'Loan Disbursal': {
                'keywords': ['loan disbursed', 'loan amount credited', 'loan sanctioned', 'disbursement'],
                'patterns': [
                    r'loan\s+(?:disbursed|credited|amount)',
                    r'disbursement\s+of',
                    r'loan\s+sanctioned',
                ]
            },
            'Monthly Salary Credit': {
                'keywords': ['salary', 'sal credited', 'payroll', 'wages', 'monthly credit', 'neft', 'transfer from'],
                'patterns': [
                    r'salary\s+(?:credited|credit)',
                    r'sal\s+credited',
                    r'payroll\s+credit',
                    r'monthly\s+(?:salary|credit)',
                    r'credited.*through\s+neft',     # credited through NEFT
                    r'transfer\s+from\s+[A-Za-z]',  # transfer from Tanya Tomar
                    r'credited.*by\s+[A-Z\s]+(?:PVT|LTD|PAYMENTS)',  # credited by PAYPAL PAYMENTS
                ]
            },
            'Refund': {
                'keywords': ['refund', 'refunded', 'will be credited', 'amount if debited'],
                'patterns': [
                    r'refund.*(?:initiated|credited|processed)',
                    r'amount\s+if\s+debited\s+will\s+get\s+refund',
                    r'refund\s+of\s+rs',
                ]
            },
            'Payment Confirmation': {
                'keywords': ['successful payment', 'payment received', 'transaction success'],
                'patterns': [
                    r'successful\s+payment.*received',
                    r'payment.*has\s+been\s+received',
                    r'transaction.*success',
                    r'receipt\s+number',
                ]
            }
        }
        
        # Accounts classification rules - improved based on CSV analysis
        self.accounts_rules = {
            'Bank Account': {
                'keywords': ['account', 'balance', 'available balance', 'current balance', 'account status', 'limit'],
                'patterns': [
                    r'(?:avl|available)\s+bal',
                    r'account\s+(?:balance|status)',
                    r'current\s+balance',
                    r'a/c\s+(?:balance|status)',
                    r'available\s+limit',
                    r'updated\s+available\s+balance',
                    r'credited\s+to\s+your.*(?:card|account)',
                    r'payment.*credited.*card',
                ]
            },
            'Loan': {
                'keywords': ['loan account', 'outstanding', 'loan status', 'loan balance', 'overdue', 'emi'],
                'patterns': [
                    r'loan\s+(?:account|status|balance)',
                    r'outstanding\s+(?:amount|balance)',
                    r'loan\s+(?:due|overdue)',
                    r'overdue\s+amt',
                    r'loan\s+a/c.*overdue',
                    r'emi.*(?:due|overdue)',
                    r'loan\s+a/c\s+[X\*]+\d+.*overdue',  # Loan A/C ****1772 has an overdue
                    r'your.*loan.*overdue',              # Your RBL Bank Loan
                ]
            },
            'Promotional Credit': {
                'keywords': ['credited', 'bonus', 'wallet', 'cashback', 'reward'],
                'patterns': [
                    r'credited.*(?:bonus|reward|cashback)',
                    r'rs\s+\d+.*credited.*wallet',
                    r'credited.*your.*(?:wallet|account)',
                    r'flat\s+\d+\s+off',
                    r'code:\s*[A-Z]+',
                ]
            }
        }
        
        # Info type classification rules
        self.info_type_rules = {
            'Outflow': {
                'keywords': ['debited', 'paid', 'spent', 'transferred', 'sent', 'withdrawn'],
                'patterns': [
                    r'(?:debited|paid|spent|transferred)\s+(?:from|by)',
                    r'amount\s+(?:debited|paid)',
                    r'txn\s+of.*(?:debited|paid)',
                ]
            },
            'Inflow': {
                'keywords': ['credited', 'received', 'deposited', 'salary', 'refund'],
                'patterns': [
                    r'(?:credited|received|deposited)\s+(?:to|in)',
                    r'amount\s+(?:credited|received)',
                    r'salary\s+credited',
                ]
            },
            'Application': {
                'keywords': ['application', 'applied', 'request', 'submitted'],
                'patterns': [
                    r'application\s+(?:submitted|received)',
                    r'request\s+(?:submitted|processed)',
                ]
            },
            'Account Status': {
                'keywords': ['status', 'balance', 'account', 'available', 'current'],
                'patterns': [
                    r'(?:account|balance)\s+(?:status|update)',
                    r'(?:avl|available|current)\s+bal',
                    r'account\s+(?:opened|closed|blocked)',
                ]
            },
            'Balance Update': {
                'keywords': ['balance', 'available', 'current balance', 'bal'],
                'patterns': [
                    r'(?:avl|available)\s+bal',
                    r'current\s+balance',
                    r'balance\s+(?:update|is)',
                ]
            }
        }
    
    def classify_sms(self, sms_text: str) -> Dict[str, str]:
        """
        Classify SMS text into sms_type, sms_event_subtype, and sms_info_type.
        
        Args:
            sms_text: Cleaned SMS text
            
        Returns:
            Dictionary with classification results
        """
        text_lower = sms_text.lower()
        
        # Determine sms_type and sms_event_subtype
        sms_type, event_subtype = self._classify_type_and_subtype(text_lower)
        
        # Determine sms_info_type
        info_type = self._classify_info_type(text_lower, sms_type, event_subtype)
        
        return {
            'sms_type': sms_type,
            'sms_event_subtype': event_subtype,
            'sms_info_type': info_type
        }
    
    def _classify_type_and_subtype(self, text: str) -> Tuple[str, str]:
        """Classify SMS type and event subtype."""
        
        # Check Purchase types
        for subtype, rules in self.purchase_rules.items():
            if self._matches_rules(text, rules):
                return 'Purchase', subtype
        
        # Check Payment types
        for subtype, rules in self.payment_rules.items():
            if self._matches_rules(text, rules):
                return 'Payment', subtype
        
        # Check Deposit & Withdrawal types
        for subtype, rules in self.deposit_withdrawal_rules.items():
            if self._matches_rules(text, rules):
                return 'Deposit & Withdrawal', subtype
        
        # Check Accounts types
        for subtype, rules in self.accounts_rules.items():
            if self._matches_rules(text, rules):
                return 'Accounts', subtype
        
        # Investment classification - improved
        investment_keywords = ['mutual fund', 'sip', 'investment', 'equity', 'bond', 'fd', 'fixed deposit',
                              'traded value', 'trading', 'nse', 'bse', 'stock exchange', 'broker', 'shares']
        investment_patterns = [
            r'traded\s+value.*rs\s*\d+',
            r'dear\s+[A-Z]+\d+[A-Z]+.*traded',
            r'national\s+stock\s+exchange',
            r'check\s+your\s+registered\s+email',
        ]

        if any(keyword in text for keyword in investment_keywords):
            return 'Investment', 'Investment'

        for pattern in investment_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return 'Investment', 'Investment'
        
        return 'Other', 'Unknown'
    
    def _classify_info_type(self, text: str, sms_type: str, event_subtype: str) -> str:
        """Classify information type based on context."""
        
        # Priority-based classification
        for info_type, rules in self.info_type_rules.items():
            if self._matches_rules(text, rules):
                # Additional context-based validation
                if self._validate_info_type(text, info_type, sms_type, event_subtype):
                    return info_type
        
        # Default classification based on sms_type
        if sms_type == 'Purchase':
            return 'Outflow'
        elif sms_type == 'Deposit & Withdrawal':
            if event_subtype in ['Loan Disbursal', 'Monthly Salary Credit']:
                return 'Inflow'
            else:
                return 'Outflow'
        elif sms_type == 'Accounts':
            return 'Account Status'
        elif sms_type == 'Payment':
            return 'Outflow'
        
        return 'Other'
    
    def _matches_rules(self, text: str, rules: Dict) -> bool:
        """Check if text matches classification rules."""
        
        # Check keywords
        for keyword in rules.get('keywords', []):
            if keyword.lower() in text:
                return True
        
        # Check patterns
        for pattern in rules.get('patterns', []):
            if re.search(pattern, text, re.IGNORECASE):
                return True
        
        return False
    
    def _validate_info_type(self, text: str, info_type: str, sms_type: str, event_subtype: str) -> bool:
        """Validate info type classification based on context."""
        
        # Outflow validation
        if info_type == 'Outflow':
            outflow_indicators = ['debited', 'paid', 'spent', 'transferred', 'withdrawn']
            return any(indicator in text for indicator in outflow_indicators)
        
        # Inflow validation
        elif info_type == 'Inflow':
            inflow_indicators = ['credited', 'received', 'deposited', 'salary', 'disbursed']
            return any(indicator in text for indicator in inflow_indicators)
        
        # Account Status validation
        elif info_type == 'Account Status':
            if sms_type == 'Accounts':
                return True
            status_indicators = ['balance', 'status', 'available', 'current']
            return any(indicator in text for indicator in status_indicators)
        
        # Balance Update validation
        elif info_type == 'Balance Update':
            balance_indicators = ['balance', 'bal', 'available', 'current']
            return any(indicator in text for indicator in balance_indicators)
        
        return True  # Default to True for other types
