#!/usr/bin/env python3
"""
Test specific fixes for the identified failed cases and edge cases.
"""

import asyncio
from typing import List, Dict, Any
from sms_parser import SMSParser


class SpecificFixTester:
    """Test specific fixes for failed cases."""
    
    def __init__(self):
        self.parser = SMSParser()
    
    async def test_failed_case_fixes(self) -> Dict[str, Any]:
        """Test fixes for specific failed cases."""
        print("🔧 Testing Specific Failed Case Fixes...")
        
        test_cases = [
            # Simpl payment failures (should be filtered as non-financial)
            {
                'text': 'Hi! You can now get your Vi prepaid invoice emailed to you, simply click https://wa.me/message/5GA7L',
                'sender': 'VP-ViCARE',
                'should_parse': False,
                'category': 'WhatsApp promotional'
            },
            {
                'text': 'Hello! We are now on Whatsapp! For Best Offers, recharges, services & more, simply click wa.me/91965',
                'sender': 'VP-ViCARE',
                'should_parse': False,
                'category': 'WhatsApp promotional'
            },
            {
                'text': 'Get JioF<PERSON> and get health Tips from <PERSON><PERSON><PERSON> through Simply Soulful\nlearn about Yoga, Meditat',
                'sender': 'JD620038',
                'should_parse': False,
                'category': 'JioFiber promotional'
            },
            {
                'text': 'Alert! Your Vi bill of Rs.315.86 was due on 02-03-2022. Please pay via Vi App or to pay online, clic',
                'sender': 'VK-ViCARE',
                'should_parse': False,
                'category': 'Bill payment reminder'
            },
            
            # Jio recharge failures (should be filtered as non-financial)
            {
                'text': '50% Daily Data quota used as on 02-May-22 20:15.\nJio Number : **********\nDaily Data quota as per pla',
                'sender': 'JP-JioPay',
                'should_parse': False,
                'category': 'Data usage notification'
            },
            
            # Stock exchange failures (should be filtered as non-financial)
            {
                'text': 'Beware while dealing based on unsolicited tips through Whatsapp, telegram, SMS, calls, etc. and take',
                'sender': 'VM-NSESMS',
                'should_parse': False,
                'category': 'Warning message'
            },
            
            # EMI loan failures (should be filtered as promotional)
            {
                'text': 'Your Money View Loan of Rs.10000.0 was disbursed in record time!! Help others make the right choice ',
                'sender': 'AD-MONVEW',
                'should_parse': False,
                'category': 'Promotional loan message'
            },
            {
                'text': 'Your Quick Loan of Rs.10,000/- is Pre-Approved. Get directly Disbursed to your Bank within 5 Mins. \n',
                'sender': '+************',
                'should_parse': False,
                'category': 'Pre-approved loan offer'
            },
            
            # Paytm transaction failures (should be parsed)
            {
                'text': 'Count#1: Rs 600 paid by 98XXXX4000 at 12:20 AM. It will settle to your bank by 7 am tomorrow or you ',
                'sender': 'BP-iPaytm',
                'should_parse': True,
                'expected_classification': 'Deposit & Withdrawal:Payment Received',
                'expected_fields': ['amount'],
                'category': 'Paytm payment received'
            },
            {
                'text': 'Count#2: Rs 260 paid by 79XXXX8939 at 4:45 PM. It will settle to your bank by 7 am tomorrow or you c',
                'sender': 'BP-iPaytm',
                'should_parse': True,
                'expected_classification': 'Deposit & Withdrawal:Payment Received',
                'expected_fields': ['amount'],
                'category': 'Paytm payment received'
            }
        ]
        
        results = {'passed': 0, 'failed': 0, 'details': []}
        
        for i, test_case in enumerate(test_cases, 1):
            try:
                parsed_results = await self.parser.parse_sms(test_case['text'])
                
                if test_case['should_parse']:
                    if parsed_results:
                        results['passed'] += 1
                        result = parsed_results[0]
                        classification = f"{result.get('sms_type')}:{result.get('sms_event_subtype')}"
                        
                        # Check fields
                        extracted_fields = []
                        for field in test_case.get('expected_fields', []):
                            if result.get(field):
                                extracted_fields.append(f"{field}: {result.get(field)}")
                        
                        results['details'].append({
                            'test': f"Fix Test {i}",
                            'category': test_case['category'],
                            'status': 'PASS',
                            'classification': classification,
                            'fields': extracted_fields
                        })
                    else:
                        results['failed'] += 1
                        results['details'].append({
                            'test': f"Fix Test {i}",
                            'category': test_case['category'],
                            'status': 'FAIL',
                            'reason': 'Should parse but did not'
                        })
                else:
                    if not parsed_results:
                        results['passed'] += 1
                        results['details'].append({
                            'test': f"Fix Test {i}",
                            'category': test_case['category'],
                            'status': 'PASS',
                            'classification': 'Correctly filtered'
                        })
                    else:
                        results['failed'] += 1
                        results['details'].append({
                            'test': f"Fix Test {i}",
                            'category': test_case['category'],
                            'status': 'FAIL',
                            'reason': 'Should be filtered but was parsed'
                        })
            
            except Exception as e:
                results['failed'] += 1
                results['details'].append({
                    'test': f"Fix Test {i}",
                    'category': test_case['category'],
                    'status': 'ERROR',
                    'reason': str(e)
                })
        
        return results
    
    async def test_edge_case_fixes(self) -> Dict[str, Any]:
        """Test fixes for edge cases with amount extraction issues."""
        print("🔧 Testing Edge Case Amount Extraction Fixes...")
        
        edge_cases = [
            # HDFC UPDATE messages with correct amount extraction
            {
                'text': 'UPDATE: INR 1,00,000.00 debited from HDFC Bank XX0930 on 07-JAN-22. Info: WITHDRAWAL SLIP NO. 2356/1',
                'expected_amount': '100000',  # Should extract 1,00,000.00 not some other number
                'category': 'HDFC UPDATE message'
            },
            {
                'text': 'UPDATE: INR 5,00,000.00 debited from HDFC Bank XX3594 on 14-OCT-21. Info: LOOSE LEAF-477 RAM SINGH S',
                'expected_amount': '500000',  # Should extract 5,00,000.00
                'category': 'HDFC UPDATE message'
            },
            {
                'text': 'UPDATE: INR 49,50,000.00 debited from HDFC Bank XX3594 on 13-OCT-21. Info: FT - Dr - XXXXXXXXXX6521',
                'expected_amount': '4950000',  # Should extract 49,50,000.00
                'category': 'HDFC high-value transaction'
            },
            # Indian Bank balance messages
            {
                'text': 'Your A/c **********  is credited by Rs. 6,000 Total Bal : Rs. 7,020.35 CR  Clr Bal : Rs. 7,020.35 CR',
                'expected_amount': '6000',  # Should extract the credited amount, not balance
                'category': 'Indian Bank credit with balance'
            }
        ]
        
        results = {'passed': 0, 'failed': 0, 'details': []}
        
        for i, test_case in enumerate(edge_cases, 1):
            try:
                parsed_results = await self.parser.parse_sms(test_case['text'])
                
                if parsed_results:
                    result = parsed_results[0]
                    extracted_amount = result.get('amount')
                    
                    if extracted_amount and str(extracted_amount).replace(',', '') == test_case['expected_amount']:
                        results['passed'] += 1
                        results['details'].append({
                            'test': f"Edge Case {i}",
                            'category': test_case['category'],
                            'status': 'PASS',
                            'extracted_amount': extracted_amount,
                            'expected_amount': test_case['expected_amount']
                        })
                    else:
                        results['failed'] += 1
                        results['details'].append({
                            'test': f"Edge Case {i}",
                            'category': test_case['category'],
                            'status': 'FAIL',
                            'reason': f'Amount mismatch: got {extracted_amount}, expected {test_case["expected_amount"]}'
                        })
                else:
                    results['failed'] += 1
                    results['details'].append({
                        'test': f"Edge Case {i}",
                        'category': test_case['category'],
                        'status': 'FAIL',
                        'reason': 'No results parsed'
                    })
            
            except Exception as e:
                results['failed'] += 1
                results['details'].append({
                    'test': f"Edge Case {i}",
                    'category': test_case['category'],
                    'status': 'ERROR',
                    'reason': str(e)
                })
        
        return results
    
    def generate_fix_report(self, failed_case_results: Dict, edge_case_results: Dict) -> str:
        """Generate comprehensive fix validation report."""
        report = []
        report.append("🎯" + "=" * 78 + "🎯")
        report.append("🔧 SPECIFIC FIXES VALIDATION REPORT")
        report.append("🎯" + "=" * 78 + "🎯")
        
        # Failed case fixes
        failed_passed = failed_case_results['passed']
        failed_failed = failed_case_results['failed']
        failed_success_rate = (failed_passed / (failed_passed + failed_failed) * 100) if (failed_passed + failed_failed) > 0 else 0
        
        report.append(f"\n📋 FAILED CASE FIXES:")
        report.append(f"  Passed: {failed_passed}")
        report.append(f"  Failed: {failed_failed}")
        report.append(f"  Success Rate: {failed_success_rate:.1f}%")
        
        if failed_success_rate >= 90:
            report.append(f"  ✅ EXCELLENT! Most failed cases fixed!")
        elif failed_success_rate >= 70:
            report.append(f"  📈 GOOD! Many failed cases fixed!")
        else:
            report.append(f"  ❌ More work needed on failed cases")
        
        # Edge case fixes
        edge_passed = edge_case_results['passed']
        edge_failed = edge_case_results['failed']
        edge_success_rate = (edge_passed / (edge_passed + edge_failed) * 100) if (edge_passed + edge_failed) > 0 else 0
        
        report.append(f"\n📋 EDGE CASE FIXES:")
        report.append(f"  Passed: {edge_passed}")
        report.append(f"  Failed: {edge_failed}")
        report.append(f"  Success Rate: {edge_success_rate:.1f}%")
        
        if edge_success_rate >= 90:
            report.append(f"  ✅ EXCELLENT! Edge cases handled well!")
        elif edge_success_rate >= 70:
            report.append(f"  📈 GOOD! Most edge cases fixed!")
        else:
            report.append(f"  ❌ More work needed on edge cases")
        
        # Overall summary
        total_passed = failed_passed + edge_passed
        total_failed = failed_failed + edge_failed
        overall_success_rate = (total_passed / (total_passed + total_failed) * 100) if (total_passed + total_failed) > 0 else 0
        
        report.append(f"\n🏆 OVERALL FIX VALIDATION:")
        report.append(f"  Total Tests: {total_passed + total_failed}")
        report.append(f"  Total Passed: {total_passed}")
        report.append(f"  Total Failed: {total_failed}")
        report.append(f"  OVERALL SUCCESS RATE: {overall_success_rate:.1f}%")
        
        if overall_success_rate >= 90:
            report.append(f"  🎉 OUTSTANDING! Fixes working excellently!")
        elif overall_success_rate >= 80:
            report.append(f"  ✅ GREAT! Most fixes working well!")
        else:
            report.append(f"  📈 Good progress, more improvements needed")
        
        # Show details for failed tests
        all_failed = [d for d in failed_case_results['details'] + edge_case_results['details'] if d['status'] in ['FAIL', 'ERROR']]
        if all_failed:
            report.append(f"\n❌ REMAINING ISSUES:")
            for test in all_failed[:5]:  # Show first 5 failures
                report.append(f"  - {test['test']} ({test['category']}): {test.get('reason', 'Unknown error')}")
        
        report.append("\n" + "🎯" + "=" * 78 + "🎯")
        
        return "\n".join(report)


async def main():
    """Main function to test specific fixes."""
    tester = SpecificFixTester()
    
    print("🚀 Starting specific fix validation")
    print("Testing fixes for identified failed cases and edge cases")
    
    # Run specific fix tests
    failed_case_results = await tester.test_failed_case_fixes()
    edge_case_results = await tester.test_edge_case_fixes()
    
    # Generate and display report
    report = tester.generate_fix_report(failed_case_results, edge_case_results)
    print(report)
    
    print(f"\n🎯 SPECIFIC FIX VALIDATION COMPLETE!")


if __name__ == "__main__":
    asyncio.run(main())
