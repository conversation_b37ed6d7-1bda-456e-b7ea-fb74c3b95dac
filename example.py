#!/usr/bin/env python3
"""
Example usage of the SMS Parser for Indian Financial Notifications.
This script demonstrates how to use the parser with various types of SMS messages.
"""

import asyncio
import json
from sms_parser import SMSParser, parse_sms_sync


def print_results(sms_text: str, results: list, title: str):
    """Helper function to print parsing results."""
    print(f"\n{'='*60}")
    print(f"📱 {title}")
    print(f"{'='*60}")
    print(f"SMS Text: {sms_text}")
    print(f"\n📊 Extracted Data:")
    
    if results:
        for i, result in enumerate(results, 1):
            print(f"\n🔍 Event {i}:")
            print(json.dumps(result, indent=2))
    else:
        print("❌ No financial data extracted")


async def async_examples():
    """Demonstrate async usage of the SMS parser."""
    parser = SMSParser()
    
    print("🚀 Async SMS Parser Examples")
    
    # Example 1: UPI Transaction
    upi_sms = """UPI txn of Rs 180.0 debited from A/c X4884 on 02May24 to <PERSON><PERSON><PERSON>eeraj J. 
    Ref no ************. Avl bal Rs 2000."""
    
    results = await parser.parse_sms(upi_sms)
    print_results(upi_sms, results, "UPI Transaction")
    
    # Example 2: Debit Card Transaction
    card_sms = """Your Debit Card XX1234 used for Rs 1,250.00 at AMAZON INDIA on 15-05-2024. 
    Txn Ref: *********012. Available Balance: Rs 45,000.00"""
    
    results = await parser.parse_sms(card_sms)
    print_results(card_sms, results, "Debit Card Transaction")
    
    # Example 3: EMI Payment
    emi_sms = """EMI of Rs 5,500 for Loan A/c ********* paid on 01Jun24. 
    EMI No: 12/36. Interest: Rs 2,100. Late fee: Rs 0. Ref: EMI789012"""
    
    results = await parser.parse_sms(emi_sms)
    print_results(emi_sms, results, "EMI Payment")
    
    # Example 4: Salary Credit
    salary_sms = """Salary of Rs 75,000 credited to A/c XX5678 on 01-06-2024 from ABC TECHNOLOGIES PVT LTD. 
    Ref: SAL240601. Available Balance: Rs 1,25,000"""
    
    results = await parser.parse_sms(salary_sms)
    print_results(salary_sms, results, "Salary Credit")


def sync_examples():
    """Demonstrate synchronous usage of the SMS parser."""
    print("\n\n🔄 Synchronous SMS Parser Examples")
    
    # Example 1: Loan Disbursal
    loan_sms = """Loan amount Rs 2,50,000 disbursed to A/c XX9876 on 10May24. 
    Loan ID: LN2024001. Lender: HDFC Bank. Ref: LN789456123"""
    
    results = parse_sms_sync(loan_sms)
    print_results(loan_sms, results, "Loan Disbursal")
    
    # Example 2: Account Balance Update
    balance_sms = """A/c XX1234 Balance Update: Available Balance Rs 15,750.50 as on 20-05-2024. 
    Account Status: Active. Last Txn: 19-05-2024"""
    
    results = parse_sms_sync(balance_sms)
    print_results(balance_sms, results, "Account Balance Update")
    
    # Example 3: Multiple Events in One SMS
    multi_sms = """UPI payment Rs 500 to Grocery Store on 25May24. Ref: *********. 
    Card payment Rs 2,000 at Petrol Pump. Card XX5678. Ref: *********. 
    Available Balance: Rs 25,000"""
    
    results = parse_sms_sync(multi_sms)
    print_results(multi_sms, results, "Multiple Events SMS")


def batch_processing_example():
    """Demonstrate batch processing of multiple SMS messages."""
    print("\n\n📦 Batch Processing Example")
    
    sms_batch = [
        "UPI payment Rs 100 to Coffee Shop on 01Jun24. Ref: *********",
        "Debit Card XX9999 used for Rs 500 at Supermarket. Ref: *********",
        "EMI Rs 3000 paid for loan XX7777. EMI No: 5/24. Ref: *********",
        "Salary Rs 60000 credited from XYZ Corp on 01-06-2024. Ref: *********"
    ]
    
    print(f"Processing {len(sms_batch)} SMS messages...")
    
    total_events = 0
    for i, sms in enumerate(sms_batch, 1):
        results = parse_sms_sync(sms)
        total_events += len(results)
        print(f"\n📱 SMS {i}: {len(results)} events extracted")
        
        for j, result in enumerate(results, 1):
            print(f"  Event {j}: {result['sms_type']} - {result['sms_event_subtype']}")
    
    print(f"\n✅ Total events extracted: {total_events}")


def field_extraction_demo():
    """Demonstrate specific field extraction capabilities."""
    print("\n\n🔧 Field Extraction Capabilities Demo")
    
    test_cases = [
        {
            "field": "Amount",
            "examples": [
                "Rs 1,250.50 debited",
                "Amount: INR 5000",
                "₹ 750 paid",
                "Txn of Rs 2,500.00"
            ]
        },
        {
            "field": "Date",
            "examples": [
                "on 02May24",
                "dated 15-05-2024", 
                "on 01 June 2024",
                "2024-06-01"
            ]
        },
        {
            "field": "Account Number",
            "examples": [
                "A/c X4884",
                "Account No: XX1234",
                "from A/c XXXX5678",
                "debited from XX9876"
            ]
        }
    ]
    
    for case in test_cases:
        print(f"\n🎯 {case['field']} Extraction:")
        for example in case['examples']:
            # Create a simple SMS with the example
            test_sms = f"Transaction {example} on 01Jun24. Ref: *********"
            results = parse_sms_sync(test_sms)
            
            if results:
                extracted_value = results[0].get(case['field'].lower().replace(' ', '_'))
                print(f"  '{example}' → {extracted_value}")
            else:
                print(f"  '{example}' → Not extracted")


def main():
    """Main function to run all examples."""
    print("🏦 SMS Parser for Indian Financial Notifications")
    print("=" * 60)
    print("This example demonstrates the capabilities of the SMS parser.")
    
    # Run async examples
    asyncio.run(async_examples())
    
    # Run sync examples
    sync_examples()
    
    # Batch processing
    batch_processing_example()
    
    # Field extraction demo
    field_extraction_demo()
    
    print("\n\n🎉 All examples completed successfully!")
    print("\nTo test with your own SMS messages, modify the examples above")
    print("or use the parser directly in your code.")


if __name__ == "__main__":
    main()
