import re
import json
from typing import List, Dict, Any, Optional, Tu<PERSON>
from collections import Counter
from datetime import datetime
import asyncio

from field_extractors import FieldExtractor
from classifiers import SMSClassifier
from utils import normalize_amount, normalize_date, clean_text


class SMSParser:
    """
    Main SMS parser for extracting structured financial data from Indian SMS notifications.
    Uses regex-based extraction with multiple patterns per field for robust matching.
    """
    
    def __init__(self):
        self.field_extractor = FieldExtractor()
        self.classifier = SMSClassifier()
    
    async def parse_sms(self, sms_text: str) -> List[Dict[str, Any]]:
        """
        Parse SMS text and extract structured financial information.
        
        Args:
            sms_text: Raw SMS text content
            
        Returns:
            List of JSON objects with extracted financial data
        """
        if not sms_text or not sms_text.strip():
            return []
        
        # Clean and normalize SMS text
        cleaned_text = clean_text(sms_text)
        
        # Split SMS into segments for multi-event detection
        segments = self._split_sms_segments(cleaned_text)
        
        results = []
        for segment in segments:
            # Classify the segment
            classification = self.classifier.classify_sms(segment)
            
            if classification['sms_type'] == 'Other':
                continue  # Skip unrecognized segments
            
            # Extract fields based on classification
            extracted_data = await self._extract_fields(segment, classification)
            
            if extracted_data:
                # Merge classification with extracted data
                result = {**classification, **extracted_data}
                results.append(result)
        
        return results
    
    def _split_sms_segments(self, sms_text: str) -> List[str]:
        """
        Split SMS into segments that might contain separate financial events.
        """
        # Common separators in Indian SMS
        separators = [
            r'\.\s*(?=[A-Z])',  # Period followed by capital letter
            r'\n+',             # Multiple newlines
            r';\s*',            # Semicolon
            r'\|\s*',           # Pipe character
            r'(?<=\.)\s*(?=(?:Ref|Txn|UPI|NEFT|IMPS|EMI|Loan))',  # Before transaction keywords
        ]
        
        segments = [sms_text]
        
        for separator in separators:
            new_segments = []
            for segment in segments:
                parts = re.split(separator, segment)
                new_segments.extend([part.strip() for part in parts if part.strip()])
            segments = new_segments
        
        # Filter out very short segments (likely noise)
        return [seg for seg in segments if len(seg) > 20]
    
    async def _extract_fields(self, segment: str, classification: Dict[str, str]) -> Dict[str, Any]:
        """
        Extract relevant fields based on SMS classification.
        """
        sms_type = classification['sms_type']
        event_subtype = classification['sms_event_subtype']
        info_type = classification['sms_info_type']
        
        extracted = {}
        
        # Common fields for all types
        extracted.update(await self._extract_common_fields(segment))
        
        # Type-specific field extraction
        if sms_type == 'Purchase':
            if event_subtype == 'UPI':
                extracted.update(await self._extract_upi_fields(segment))
            elif event_subtype == 'Debit Card':
                extracted.update(await self._extract_card_fields(segment))
        
        elif sms_type == 'Payment':
            if event_subtype == 'EMI Payment':
                extracted.update(await self._extract_emi_fields(segment))
        
        elif sms_type == 'Deposit & Withdrawal':
            if event_subtype == 'Loan Disbursal':
                extracted.update(await self._extract_loan_disbursal_fields(segment))
            elif event_subtype == 'Monthly Salary Credit':
                extracted.update(await self._extract_salary_fields(segment))
        
        elif sms_type == 'Accounts':
            if event_subtype == 'Bank Account':
                extracted.update(await self._extract_bank_account_fields(segment))
            elif event_subtype == 'Loan':
                extracted.update(await self._extract_loan_account_fields(segment))
        
        # Remove None values
        return {k: v for k, v in extracted.items() if v is not None}
    
    async def _extract_common_fields(self, text: str) -> Dict[str, Any]:
        """Extract fields common to most SMS types."""
        return {
            'amount': await self.field_extractor.extract_amount(text),
            'date': await self.field_extractor.extract_date(text),
            'account_number': await self.field_extractor.extract_account_number(text),
            'bank_name': await self.field_extractor.extract_bank_name(text),
            'txn_ref': await self.field_extractor.extract_transaction_ref(text),
            'currency': await self.field_extractor.extract_currency(text),
        }
    
    async def _extract_upi_fields(self, text: str) -> Dict[str, Any]:
        """Extract UPI-specific fields."""
        return {
            'upi_recipient': await self.field_extractor.extract_upi_recipient(text),
        }
    
    async def _extract_card_fields(self, text: str) -> Dict[str, Any]:
        """Extract debit/credit card specific fields."""
        return {
            'card_number': await self.field_extractor.extract_card_number(text),
            'merchant_name': await self.field_extractor.extract_merchant_name(text),
        }
    
    async def _extract_emi_fields(self, text: str) -> Dict[str, Any]:
        """Extract EMI payment specific fields."""
        return {
            'emi_amount': await self.field_extractor.extract_emi_amount(text),
            'emi_number': await self.field_extractor.extract_emi_number(text),
            'due_date': await self.field_extractor.extract_due_date(text),
            'interest_charged': await self.field_extractor.extract_interest_charged(text),
            'late_fee_charged': await self.field_extractor.extract_late_fee(text),
            'is_loan_repayment': await self.field_extractor.extract_loan_repayment_flag(text),
            'is_loan_delayed': await self.field_extractor.extract_loan_delayed_flag(text),
        }
    
    async def _extract_loan_disbursal_fields(self, text: str) -> Dict[str, Any]:
        """Extract loan disbursal specific fields."""
        return {
            'loan_id': await self.field_extractor.extract_loan_id(text),
            'lender_name': await self.field_extractor.extract_lender_name(text),
        }
    
    async def _extract_salary_fields(self, text: str) -> Dict[str, Any]:
        """Extract salary credit specific fields."""
        return {
            'employer_name': await self.field_extractor.extract_employer_name(text),
            'is_salary': await self.field_extractor.extract_salary_flag(text),
        }
    
    async def _extract_bank_account_fields(self, text: str) -> Dict[str, Any]:
        """Extract bank account status fields."""
        return {
            'account_status': await self.field_extractor.extract_account_status(text),
            'account_sub_type': await self.field_extractor.extract_account_sub_type(text),
            'account_opening_date': await self.field_extractor.extract_account_opening_date(text),
            'current_amount': await self.field_extractor.extract_current_balance(text),
        }
    
    async def _extract_loan_account_fields(self, text: str) -> Dict[str, Any]:
        """Extract loan account status fields."""
        return {
            'loan_id': await self.field_extractor.extract_loan_id(text),
            'account_status': await self.field_extractor.extract_account_status(text),
            'current_amount': await self.field_extractor.extract_outstanding_amount(text),
            'default_status': await self.field_extractor.extract_default_status(text),
            'lender_name': await self.field_extractor.extract_lender_name(text),
        }


# Convenience function for synchronous usage
def parse_sms_sync(sms_text: str) -> List[Dict[str, Any]]:
    """Synchronous wrapper for SMS parsing."""
    parser = SMSParser()
    return asyncio.run(parser.parse_sms(sms_text))


if __name__ == "__main__":
    # Example usage
    sample_sms = """
    UPI txn of Rs 180.0 debited from A/c X4884 on 02May24 to Dheeraj Neeraj J. 
    Ref no ************. Avl bal Rs 2000.
    """
    
    result = parse_sms_sync(sample_sms)
    print(json.dumps(result, indent=2))
