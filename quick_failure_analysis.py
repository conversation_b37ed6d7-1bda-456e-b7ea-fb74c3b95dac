#!/usr/bin/env python3
"""
Quick analysis of failed SMS cases to identify patterns for 100% success.
"""

import csv
import re
from collections import defaultdict, Counter
from sms_parser import parse_sms_sync


def load_sample_sms(limit=200):
    """Load a sample of SMS messages for quick analysis."""
    messages = []
    
    try:
        with open('sms_backup.csv', 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            
            count = 0
            for row in reader:
                if count >= limit:
                    break
                    
                text = row.get('text', '').strip()
                sender = row.get('senderAddress', '').strip()
                
                # Quick financial check
                if any(keyword in text.lower() for keyword in ['rs', '₹', 'debited', 'credited', 'upi', 'card', 'balance', 'account', 'payment', 'transaction']):
                    messages.append({
                        'text': text,
                        'sender': sender,
                        'id': row.get('id', '')
                    })
                    count += 1
    
    except Exception as e:
        print(f"Error loading CSV: {e}")
        return []
    
    return messages


def analyze_failures():
    """Analyze failed cases and suggest improvements."""
    print("🎯 QUICK FAILURE ANALYSIS FOR 100% SUCCESS RATE")
    print("=" * 60)
    
    # Load sample messages
    messages = load_sample_sms(200)
    print(f"Loaded {len(messages)} financial SMS messages for analysis")
    
    failed_cases = []
    success_cases = []
    
    # Test each message
    for i, msg in enumerate(messages):
        try:
            results = parse_sms_sync(msg['text'])
            
            if results:
                success_cases.append(msg)
            else:
                failed_cases.append(msg)
        
        except Exception as e:
            failed_cases.append({**msg, 'error': str(e)})
    
    print(f"\n📊 Results:")
    print(f"Success: {len(success_cases)}")
    print(f"Failed: {len(failed_cases)}")
    print(f"Success rate: {len(success_cases)/(len(success_cases)+len(failed_cases))*100:.1f}%")
    
    # Analyze failed cases
    print(f"\n🔍 ANALYZING {len(failed_cases)} FAILED CASES:")
    
    failed_by_sender = defaultdict(list)
    failure_patterns = {
        'promotional': [],
        'otp_verification': [],
        'non_transaction': [],
        'missing_amount': [],
        'missing_classification': [],
        'complex_format': []
    }
    
    for case in failed_cases:
        text = case['text'].lower()
        sender = case['sender']
        
        failed_by_sender[sender].append(case['text'])
        
        # Categorize failure types
        if any(word in text for word in ['offer', 'discount', 'promo', 'sale', 'free', 'win', 'congratulations']):
            failure_patterns['promotional'].append(case)
        elif any(word in text for word in ['otp', 'verification', 'code', 'pin', 'verify']):
            failure_patterns['otp_verification'].append(case)
        elif not any(word in text for word in ['rs', '₹', 'inr', 'amount', 'balance', 'debited', 'credited']):
            failure_patterns['non_transaction'].append(case)
        elif any(word in text for word in ['rs', '₹', 'inr']) and not re.search(r'rs\.?\s*\d+|₹\s*\d+|inr\s*\d+', text):
            failure_patterns['missing_amount'].append(case)
        else:
            failure_patterns['complex_format'].append(case)
    
    # Print analysis
    print(f"\n📱 TOP FAILING SENDERS:")
    sender_counts = Counter({k: len(v) for k, v in failed_by_sender.items()})
    for sender, count in sender_counts.most_common(10):
        print(f"  {sender}: {count} failures")
        # Show one example
        if failed_by_sender[sender]:
            print(f"    Example: {failed_by_sender[sender][0][:80]}...")
    
    print(f"\n📋 FAILURE CATEGORIES:")
    for category, cases in failure_patterns.items():
        print(f"  {category}: {len(cases)} cases")
        if cases:
            print(f"    Example: {cases[0]['text'][:80]}...")
    
    # Focus on complex format cases that should be parseable
    print(f"\n🎯 COMPLEX FORMAT CASES TO FIX:")
    for i, case in enumerate(failure_patterns['complex_format'][:10], 1):
        print(f"{i}. Sender: {case['sender']}")
        print(f"   Text: {case['text']}")
        print()
    
    return failed_cases, failure_patterns


def suggest_improvements(failed_cases, failure_patterns):
    """Suggest specific improvements for 100% success rate."""
    print(f"\n💡 IMPROVEMENT SUGGESTIONS FOR 100% SUCCESS:")
    print("=" * 60)
    
    suggestions = []
    
    # 1. Handle promotional/non-financial messages
    non_financial = len(failure_patterns['promotional']) + len(failure_patterns['otp_verification']) + len(failure_patterns['non_transaction'])
    if non_financial > 0:
        suggestions.append(f"1. Filter out {non_financial} non-financial messages")
        suggestions.append("   - Add better non-financial detection")
        suggestions.append("   - Exclude OTP, promotional, and info messages")
    
    # 2. Improve amount extraction
    if failure_patterns['missing_amount']:
        suggestions.append(f"2. Fix amount extraction for {len(failure_patterns['missing_amount'])} cases")
        for case in failure_patterns['missing_amount'][:3]:
            suggestions.append(f"   - Example: {case['text'][:60]}...")
    
    # 3. Add new classification patterns
    if failure_patterns['complex_format']:
        suggestions.append(f"3. Add patterns for {len(failure_patterns['complex_format'])} complex formats")
        
        # Analyze common words in complex cases
        all_text = ' '.join([case['text'].lower() for case in failure_patterns['complex_format']])
        words = re.findall(r'\b\w+\b', all_text)
        word_counts = Counter(words)
        
        financial_words = []
        for word, count in word_counts.most_common(20):
            if count >= 2 and len(word) > 3 and word not in ['your', 'have', 'been', 'will', 'this', 'that']:
                financial_words.append(word)
        
        if financial_words:
            suggestions.append("   - Add keywords: " + ", ".join(financial_words[:10]))
    
    # Print suggestions
    for suggestion in suggestions:
        print(suggestion)
    
    return suggestions


def main():
    """Main function."""
    failed_cases, failure_patterns = analyze_failures()
    suggestions = suggest_improvements(failed_cases, failure_patterns)
    
    print(f"\n🚀 NEXT STEPS:")
    print("1. Implement better non-financial message filtering")
    print("2. Add missing regex patterns for complex formats")
    print("3. Improve classification rules")
    print("4. Test with full dataset")


if __name__ == "__main__":
    main()
