{"overall_stats": {"total_tested": 631, "total_parsed": 389, "total_filtered": 209, "total_failed": 33, "success_rate": 92.**************, "filter_rate": 33.**************}, "category_results": {"upi_transactions": {"category": "upi_transactions", "tested": 50, "parsed": 50, "filtered": 0, "failed": 0, "fields_extracted": {"sms_info_type": 96, "bank_name": 68, "currency": 96, "amount": 49, "account_number": 49, "txn_ref": 48, "upi_recipient": 5, "date": 3}, "classifications": {"Accounts:Bank Account": 47, "Purchase:UPI": 49}, "edge_cases": [{"message": "Your A/c XXX1824658  is credited by Rs. 6,000 Total Bal : Rs. 7,020.35 CR  Clr Bal : Rs. 7,020.35 CR", "sender": "BV-INDBNK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Inflow", "amount": "7020.35", "date": "13-04-2022", "account_number": "XXX182465", "currency": "INR"}}], "failed_cases": []}, "hdfc_transactions": {"category": "hdfc_transactions", "tested": 42, "parsed": 42, "filtered": 0, "failed": 0, "fields_extracted": {"sms_info_type": 65, "amount": 57, "date": 27, "account_number": 46, "txn_ref": 27, "currency": 65, "bank_name": 11, "card_number": 4}, "classifications": {"Accounts:Bank Account": 46, "Purchase:Debit Card": 19}, "edge_cases": [{"message": "UPDATE: INR 1,00,000.00 debited from HDFC Bank XX0930 on 07-JAN-22. Info: WITHDRAWAL SLIP NO. 2356/1", "sender": "QP-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "142761.44", "currency": "INR"}}, {"message": "UPDATE: INR 5,00,000.00 debited from HDFC Bank XX3594 on 14-OCT-21. Info: LOOSE LEAF-477 RAM SINGH S", "sender": "AD-HDFCBK", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "500000", "account_number": "XX3594", "bank_name": "HDFC", "currency": "INR"}}, {"message": "UPDATE: INR 5,00,000.00 debited from HDFC Bank XX3594 on 14-OCT-21. Info: LOOSE LEAF-477 RAM SINGH S", "sender": "AD-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "1029659.2", "currency": "INR"}}, {"message": "UPDATE: INR 5,26,000.00 debited from HDFC Bank XX3594 on 13-OCT-21. Info: FT - Dr - XXXXXXXXXX4398 -", "sender": "AD-HDFCBK", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "526000", "account_number": "XX3594", "bank_name": "HDFC", "currency": "INR"}}, {"message": "UPDATE: INR 5,26,000.00 debited from HDFC Bank XX3594 on 13-OCT-21. Info: FT - Dr - XXXXXXXXXX4398 -", "sender": "AD-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "1529659.2", "currency": "INR"}}, {"message": "UPDATE: INR 49,50,000.00 debited from HDFC Bank XX3594 on 13-OCT-21. Info: FT - Dr - XXXXXXXXXX6521 ", "sender": "QP-HDFCBK", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "4950000", "account_number": "XX3594", "bank_name": "HDFC", "currency": "INR"}}, {"message": "UPDATE: INR 49,50,000.00 debited from HDFC Bank XX3594 on 13-OCT-21. Info: FT - Dr - XXXXXXXXXX6521 ", "sender": "QP-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "2055659.2", "currency": "INR"}}, {"message": "UPDATE: INR 11,800.00 debited from HDFC Bank XX3594 on 16-SEP-21. Info: MC CHARGES INCL GST 080921. ", "sender": "AD-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "6913598.2", "currency": "INR"}}, {"message": "UPDATE: INR 42,00,000.00 debited from HDFC Bank XX3594 on 08-SEP-21. Info: MC Issued - KAMBALA - 901", "sender": "AX-HDFCBK", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "4200000", "account_number": "XX3594", "bank_name": "HDFC", "currency": "INR"}}, {"message": "UPDATE: INR 42,00,000.00 debited from HDFC Bank XX3594 on 08-SEP-21. Info: MC Issued - KAMBALA - 901", "sender": "AX-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "6975398.2", "currency": "INR"}}, {"message": "UPDATE: INR 2,00,000.00 debited from HDFC Bank XX0930 on 27-AUG-21. Info: 2356/1107, RAM SINGH - CAS", "sender": "JK-HDFCBK", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "200000", "account_number": "XX0930", "bank_name": "HDFC", "currency": "INR"}}, {"message": "UPDATE: INR 2,00,000.00 debited from HDFC Bank XX0930 on 27-AUG-21. Info: 2356/1107, RAM SINGH - CAS", "sender": "JK-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "120065.98", "currency": "INR"}}, {"message": "UPDATE: INR 40,000.00 debited from HDFC Bank XX0930 on 16-AUG-21. Info: FT -KULWINDER SINGH Dr - XXX", "sender": "JD-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "270065.98", "currency": "INR"}}, {"message": "UPDATE: INR 5,00,000.00 debited from HDFC Bank XX3594 on 16-AUG-21. Info: FT - Dr - XXXXXXXXXX5201 -", "sender": "JD-HDFCBK", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "500000", "account_number": "XX3594", "bank_name": "HDFC", "currency": "INR"}}, {"message": "UPDATE: INR 5,00,000.00 debited from HDFC Bank XX3594 on 16-AUG-21. Info: FT - Dr - XXXXXXXXXX5201 -", "sender": "JD-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "********.2", "currency": "INR"}}, {"message": "UPDATE: INR 52,250.00 debited from HDFC Bank XX0930 on 05-AUG-21. Info: HLICCON - ************* - By", "sender": "MD-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "460065.98", "currency": "INR"}}, {"message": "UPDATE: INR 1,00,000.00 debited from A/c XX3594 on 15-JUN-21. Info: slip no 436/9011,cash withdrawal", "sender": "MD-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "********.9", "currency": "INR"}}, {"message": "UPDATE: INR 5,00,000.00 debited from A/c XX3594 on 20-MAY-21. Info: slip no 426/9011,cash withdrawal", "sender": "MD-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Outflow", "amount": "500000", "account_number": "XX3594", "currency": "INR"}}, {"message": "UPDATE: INR 5,00,000.00 debited from A/c XX3594 on 20-MAY-21. Info: slip no 426/9011,cash withdrawal", "sender": "MD-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "********.9", "currency": "INR"}}, {"message": "UPDATE: INR 4,00,000.00 debited from A/c XX3594 on 23-APR-21. Info: RTGS Dr-PSIB0000230-SUCHA SINGH-", "sender": "VK-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Outflow", "amount": "400000", "account_number": "XX3594", "currency": "INR"}}, {"message": "UPDATE: INR 4,00,000.00 debited from A/c XX3594 on 23-APR-21. Info: RTGS Dr-PSIB0000230-SUCHA SINGH-", "sender": "VK-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "1510628.6", "currency": "INR"}}, {"message": "UPDATE: INR 4,00,000.00 debited from A/c XX3594 on 23-APR-21. Info: FT - Dr - XXXXXXXXXX2551 - RAVI ", "sender": "VK-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Outflow", "amount": "400000", "account_number": "XX3594", "currency": "INR"}}, {"message": "UPDATE: INR 4,00,000.00 debited from A/c XX3594 on 23-APR-21. Info: FT - Dr - XXXXXXXXXX2551 - RAVI ", "sender": "VK-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "1910628.6", "currency": "INR"}}], "failed_cases": []}, "simpl_payments": {"category": "simpl_payments", "tested": 8, "parsed": 5, "filtered": 0, "failed": 3, "fields_extracted": {"sms_info_type": 9, "amount": 5, "currency": 9, "merchant_name": 2, "default_status": 2}, "classifications": {"Purchase:Debit Card": 6, "Accounts:Bank Account": 1, "Accounts:Loan": 2}, "edge_cases": [{"message": "Rs.209.0 for Vodafone Idea, Mobile Number **********, on 6 May 2022 11:59 PM charged to your Simpl a", "sender": "CP-SmplPL", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "209", "currency": "INR"}}, {"message": "Rs.209.0 for Vodafone Idea, Mobile Number **********, on 6 May 2022 11:59 PM charged to your Simpl a", "sender": "CP-SmplPL", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "Rs.209.0 for Vodafone Idea, Mobile Number **********, on 6 May 2022 11:59 PM charged to your Simpl a", "sender": "CP-SmplPL", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "Alert! Your Vi bill of Rs.315.86 is overdue. Please make an immediate payment to enjoy uninterrupted", "sender": "VK-ViCARE", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Loan", "sms_info_type": "Account Status", "amount": "315.86", "currency": "INR", "default_status": "overdue"}}, {"message": "Alert! Your Vi bill of Rs.315.86 is overdue.To enjoy uninterrupted services, pay now using Vi App or", "sender": "VK-ViCARE", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Loan", "sms_info_type": "Account Status", "amount": "315.86", "currency": "INR", "default_status": "overdue"}}], "failed_cases": [{"message": "Alert! Your Vi bill of Rs.315.86 was due on 02-03-2022. Please pay via Vi App or to pay online, clic", "sender": "VK-ViCARE", "category": "simpl_payments"}, {"message": "Hi! Your Vi bill of Rs.315.86 is due today 02-03-2022. To pay now via Vi App or to pay online, click", "sender": "VK-ViCARE", "category": "simpl_payments"}, {"message": "Hi! Your Vi bill of Rs.315.86 is due on 02-03-2022. Please pay by due date to avoid late payment cha", "sender": "VK-ViCARE", "category": "simpl_payments"}]}, "jio_recharges": {"category": "jio_recharges", "tested": 50, "parsed": 21, "filtered": 5, "failed": 24, "fields_extracted": {"sms_info_type": 32, "currency": 32, "amount": 14, "upi_recipient": 3}, "classifications": {"Purchase:UPI": 22, "Accounts:Bank Account": 6, "Accounts:Promotional Credit": 4}, "edge_cases": [{"message": "90% daily data quota used as on 03-May-22 00:44.\nJio Number : **********\nFor tips on how to manage d", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "Recharge of Rs. 15.0 is successful for your Jio number **********.\nEntitlement: Benefits: Unlimited ", "sender": "JD-JioPay", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Outflow", "amount": "15", "currency": "INR"}}, {"message": "Recharge of Rs. 15.0 is successful for your Jio number **********.\nEntitlement: Benefits: Unlimited ", "sender": "JD-JioPay", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "90% daily data quota used as on 28-Apr-22 16:37.\nJio Number : **********\nFor tips on how to manage d", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "90% daily data quota used as on 27-Apr-22 23:44.\nJio Number : **********\nFor tips on how to manage d", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "90% daily data quota used as on 27-Apr-22 00:27.\nJio Number : **********\nFor tips on how to manage d", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "90% daily data quota used as on 22-Apr-22 20:23.\nJio Number : **********\nFor tips on how to manage d", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "Plan expired! Recharge now on Paytm & get upto Rs.100 Cashback - Code JIO100 each time for self & fa", "sender": "JM-620016", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Outflow", "amount": "100", "currency": "INR", "upi_recipient": "Rs"}}, {"message": "Plan expired! Recharge now on Paytm & get upto Rs.100 Cashback - Code JIO100 each time for self & fa", "sender": "JM-620016", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Promotional Credit", "sms_info_type": "Account Status", "amount": "30", "currency": "INR"}}, {"message": "Plan expired! Recharge now on Paytm & get upto Rs.100 Cashback - Code JIO100 each time for self & fa", "sender": "JM-620016", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Outflow", "amount": "239", "currency": "INR"}}, {"message": "Recharge of Rs. 239.00 is successful for your Jio number **********.\nEntitlement: Benefits: \n1. UNLI", "sender": "JD-JioPay", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Outflow", "amount": "239", "currency": "INR"}}, {"message": "Recharge of Rs. 239.00 is successful for your Jio number **********.\nEntitlement: Benefits: \n1. UNLI", "sender": "JD-JioPay", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "Recharge of Rs. 239.00 is successful for your Jio number **********.\nEntitlement: Benefits: \n1. UNLI", "sender": "JD-JioPay", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "Hurry! Recharge Jio no.********** on Amazon & get upto Rs.300 daily rewards for self & family. Upto ", "sender": "JK-620016", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Outflow", "amount": "300", "currency": "INR", "upi_recipient": "Rs"}}, {"message": "Hurry! Recharge Jio no.********** on Amazon & get upto Rs.300 daily rewards for self & family. Upto ", "sender": "JK-620016", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Promotional Credit", "sms_info_type": "Account Status", "amount": "50", "currency": "INR"}}, {"message": "Hurry! Recharge Jio no.********** on Amazon & get upto Rs.300 daily rewards for self & family. Upto ", "sender": "JK-620016", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Outflow", "amount": "239", "currency": "INR"}}, {"message": "Your plan Rs 239-1m-1.5GB/D for Jio Number ********** has expired on 19-Apr-22 09:25 Hrs. To continu", "sender": "JP-JIOPAY", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Outflow", "currency": "INR"}}, {"message": "90% daily data quota used as on 18-Apr-22 22:54.\nJio Number : **********\nFor tips on how to manage d", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "Hurry! Recharge Jio no.********** on Paytm & get upto Rs.100 Cashback - Code JIO100 each time for se", "sender": "JX-620016", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Promotional Credit", "sms_info_type": "Account Status", "amount": "30", "currency": "INR"}}, {"message": "Hurry! Recharge Jio no.********** on Paytm & get upto Rs.100 Cashback - Code JIO100 each time for se", "sender": "JX-620016", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Outflow", "amount": "239", "currency": "INR"}}, {"message": "90% daily data quota used as on 17-Apr-22 16:55.\nJio Number : **********\nFor tips on how to manage d", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "90% daily data quota used as on 15-Apr-22 20:44.\nJio Number : **********\nFor tips on how to manage d", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "Recharge your Jio no. ********** today with Rs.299 plan. Recharge on Amazon. Existing user gets upto", "sender": "JX-620016", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Outflow", "amount": "299", "currency": "INR"}}, {"message": "Recharge your Jio no. ********** today with Rs.299 plan. Recharge on Amazon. Existing user gets upto", "sender": "JX-620016", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Promotional Credit", "sms_info_type": "Account Status", "amount": "300", "currency": "INR"}}, {"message": "90% daily data quota used as on 11-Apr-22 23:39.\nJio Number : **********\nFor tips on how to manage d", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "You have exhausted your daily SMS quota from Rs 239-1m-1.5GB/D for Jio number ********** as on 11-Ap", "sender": "JP-JIOPAY", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "You have consumed 90% of the daily 100.0 Units SMS quota from Rs 239-1m-1.5GB/D on Jio Number 628036", "sender": "JP-JIOPAY", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "As per government regulation, you are allowed to send only 100 free SMS's per day and you have alrea", "sender": "JP-JioSvc", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "As per government regulation, you are allowed to send only 100 free SMS's per day and you have alrea", "sender": "JP-JioSvc", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "90% daily data quota used as on 10-Apr-22 00:51.\nJio Number : **********\nFor tips on how to manage d", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "currency": "INR"}}], "failed_cases": [{"message": "50% Daily Data quota used as on 02-May-22 20:15.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 02-May-22 01:07.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 01-May-22 00:41.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 29-Apr-22 23:40.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 28-Apr-22 14:15.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 27-Apr-22 14:54.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 26-Apr-22 18:07.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 25-Apr-22 21:17.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 24-Apr-22 21:40.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 23-Apr-22 18:25.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 22-Apr-22 14:31.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 21-Apr-22 19:43.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 20-Apr-22 19:45.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 18-Apr-22 20:17.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "Your current plan Rs 239-1m-1.5GB/D for Jio number ********** will expire on 19-Apr-22 09:25 Hrs. Af", "sender": "JP-JIOPAY", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 17-Apr-22 14:03.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 15-Apr-22 12:27.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 14-Apr-22 22:18.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 14-Apr-22 00:36.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 12-Apr-22 23:34.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "You have consumed 50% of the daily 100.0 Units SMS quota from Rs 239-1m-1.5GB/D on Jio Number 628036", "sender": "JP-JIOPAY", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 11-Apr-22 20:46.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 09-Apr-22 23:30.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 08-Apr-22 17:09.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}]}, "stock_exchange": {"category": "stock_exchange", "tested": 38, "parsed": 36, "filtered": 0, "failed": 2, "fields_extracted": {"sms_info_type": 104, "amount": 36, "currency": 104, "current_amount": 36, "bank_name": 36}, "classifications": {"Accounts:Bank Account": 72, "Investment:Investment": 32}, "edge_cases": [{"message": "NEXTBILLION TECHNOLOGY PRIVATE on 02-apr-2022 reported your Fund bal Rs.0.67 & Securities bal 0. Thi", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0.67", "currency": "INR", "current_amount": "0.67"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0.67", "currency": "INR", "current_amount": "0.67"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0.67", "currency": "INR", "current_amount": "0.67"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0.67", "currency": "INR", "current_amount": "0.67"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0.67", "currency": "INR", "current_amount": "0.67"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "JM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "JM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "JM-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0 & securiti", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0 & securiti", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0 & securiti", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0 & securiti", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0 & securiti", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0 & securiti", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "NEXTBILLION TECHNOLOGY PRIVATE on 09-apr-2022 reported your Fund bal Rs.0.67 & Securities bal 0. Thi", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0.67", "currency": "INR", "current_amount": "0.67"}}, {"message": "5PAISA CAPITAL LIMITED on 02-apr-2022 reported your Fund bal Rs.-147.71 & Securities bal 0. This exc", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "NEXTBILLION TECHNOLOGY PRIVATE on 02-apr-2022 reported your Fund bal Rs.0.67 & Securities bal 0. Thi", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0.67", "currency": "INR", "current_amount": "0.67"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0.67", "currency": "INR", "current_amount": "0.67"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0.67", "currency": "INR", "current_amount": "0.67"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0.67", "currency": "INR", "current_amount": "0.67"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0.67", "currency": "INR", "current_amount": "0.67"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & secur", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.-147.71 & securities balance", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "JM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "JM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "JM-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker 5PAISA CAPITAL LIMITED. reported your fund balance Rs.0 & securities balance 0 as ", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0 & securiti", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0 & securiti", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0 & securiti", "sender": "VM-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0 & securiti", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "0", "currency": "INR", "current_amount": "0"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0 & securiti", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your bank", "currency": "INR"}}, {"message": "Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0 & securiti", "sender": "VK-NSESMS", "parsed_result": {"sms_type": "Investment", "sms_event_subtype": "Investment", "sms_info_type": "Other", "currency": "INR"}}], "failed_cases": [{"message": "Beware while dealing based on unsolicited tips through Whatsapp, telegram, SMS, calls, etc. and take", "sender": "VM-NSESMS", "category": "stock_exchange"}, {"message": "Beware while dealing based on unsolicited tips through Whatsapp, telegram, SMS, calls, etc. and take", "sender": "VM-NSESMS", "category": "stock_exchange"}]}, "emi_loans": {"category": "emi_loans", "tested": 50, "parsed": 46, "filtered": 0, "failed": 4, "fields_extracted": {"sms_info_type": 63, "amount": 35, "currency": 63, "loan_id": 10, "account_number": 13, "bank_name": 26, "is_loan_repayment": 30, "date": 10, "emi_amount": 20, "due_date": 8, "is_loan_delayed": 2}, "classifications": {"Deposit & Withdrawal:Loan Disbursal": 7, "Payment:EMI Payment": 35, "Accounts:Bank Account": 14, "Accounts:Loan": 7}, "edge_cases": [{"message": "Dear Customer, your 200,000 Rs loan has been disbursed. Click on the link to confirm withdrawal http", "sender": "JD-ANKIAR", "parsed_result": {"sms_type": "Deposit & Withdrawal", "sms_event_subtype": "<PERSON>an <PERSON>bursal", "sms_info_type": "Inflow", "amount": "200000", "currency": "INR", "loan_id": "has"}}, {"message": "Dear DEVWART, EMI of Rs 1841. for your Home Credit Loan ********** is due on 17-10-21. Please mainta", "sender": "VK-HOMECR", "parsed_result": {"sms_type": "Payment", "sms_event_subtype": "EMI Payment", "sms_info_type": "Outflow", "amount": "1841", "date": "17-10-2021", "currency": "INR", "emi_amount": "1841", "due_date": "17-10-2021", "is_loan_repayment": true, "is_loan_delayed": false}}, {"message": "\"GENTLE REMINDER! Dear DEVWART, EMI of Rs 2049. for your Home Credit Loan ********** is due Tomorrow", "sender": "JK-HOMECR", "parsed_result": {"sms_type": "Payment", "sms_event_subtype": "EMI Payment", "sms_info_type": "Outflow", "amount": "2049", "currency": "INR", "emi_amount": "2049", "is_loan_repayment": true, "is_loan_delayed": false}}, {"message": "\"GENTLE REMINDER! Dear DEVWART, EMI of Rs 2049. for your Home Credit Loan ********** is due Tomorrow", "sender": "JK-HOMECR", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "sufficient bank", "currency": "INR"}}, {"message": "Dear DEVWART, in view of the current COVID19, we urge you to stay home. Please maintain sufficient b", "sender": "JD-HOMECR", "parsed_result": {"sms_type": "Payment", "sms_event_subtype": "EMI Payment", "sms_info_type": "Account Status", "amount": "2031", "date": "18-09-2021", "bank_name": "in bank", "currency": "INR", "emi_amount": "2031", "due_date": "18-09-2021", "is_loan_repayment": true, "is_loan_delayed": false}}, {"message": "\"GENTLE REMINDER! Dear DEVWART, EMI of Rs 1841. for your Home Credit Loan ********** is due Tomorrow", "sender": "JD-HOMECR", "parsed_result": {"sms_type": "Payment", "sms_event_subtype": "EMI Payment", "sms_info_type": "Outflow", "amount": "1841", "currency": "INR", "emi_amount": "1841", "is_loan_repayment": true, "is_loan_delayed": false}}, {"message": "\"GENTLE REMINDER! Dear DEVWART, EMI of Rs 1841. for your Home Credit Loan ********** is due Tomorrow", "sender": "JD-HOMECR", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "sufficient bank", "currency": "INR"}}, {"message": "\"GENTLE REMINDER! Dear DEVWART, EMI of Rs 2049. for your Home Credit Loan ********** is due Tomorrow", "sender": "JD-HOMECR", "parsed_result": {"sms_type": "Payment", "sms_event_subtype": "EMI Payment", "sms_info_type": "Outflow", "amount": "2049", "currency": "INR", "emi_amount": "2049", "is_loan_repayment": true, "is_loan_delayed": false}}, {"message": "\"GENTLE REMINDER! Dear DEVWART, EMI of Rs 2049. for your Home Credit Loan ********** is due Tomorrow", "sender": "JD-HOMECR", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "sufficient bank", "currency": "INR"}}, {"message": "\"GENTLE REMINDER! Dear DEVWART, EMI of Rs 2049. for your Home Credit Loan ********** is due Tomorrow", "sender": "MD-HOMECR", "parsed_result": {"sms_type": "Payment", "sms_event_subtype": "EMI Payment", "sms_info_type": "Outflow", "amount": "2049", "currency": "INR", "emi_amount": "2049", "is_loan_repayment": true, "is_loan_delayed": false}}, {"message": "\"GENTLE REMINDER! Dear DEVWART, EMI of Rs 2049. for your Home Credit Loan ********** is due Tomorrow", "sender": "MD-HOMECR", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "sufficient bank", "currency": "INR"}}, {"message": "Dear DEVWART, in view of the current COVID19, we urge you to stay home. Please maintain sufficient b", "sender": "MD-HOMECR", "parsed_result": {"sms_type": "Payment", "sms_event_subtype": "EMI Payment", "sms_info_type": "Account Status", "amount": "2031", "date": "18-07-2021", "bank_name": "in bank", "currency": "INR", "emi_amount": "2031", "due_date": "18-07-2021", "is_loan_repayment": true, "is_loan_delayed": false}}, {"message": "Hi, Payment Reminder! Please pay your pending dues in just 1 click - Log on to <PERSON><PERSON> App. Click on O", "sender": "AX-IDHANI", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "Dear Customer, your 200,000 Rs loan has been disbursed. Click on the link to confirm withdrawal http", "sender": "JD-ANKIAR", "parsed_result": {"sms_type": "Deposit & Withdrawal", "sms_event_subtype": "<PERSON>an <PERSON>bursal", "sms_info_type": "Inflow", "amount": "200000", "currency": "INR", "loan_id": "has"}}, {"message": "Dear DEVWART, EMI of Rs 1841. for your Home Credit Loan ********** is due on 17-10-21. Please mainta", "sender": "VK-HOMECR", "parsed_result": {"sms_type": "Payment", "sms_event_subtype": "EMI Payment", "sms_info_type": "Outflow", "amount": "1841", "date": "17-10-2021", "currency": "INR", "emi_amount": "1841", "due_date": "17-10-2021", "is_loan_repayment": true, "is_loan_delayed": false}}, {"message": "\"GENTLE REMINDER! Dear DEVWART, EMI of Rs 2049. for your Home Credit Loan ********** is due Tomorrow", "sender": "JK-HOMECR", "parsed_result": {"sms_type": "Payment", "sms_event_subtype": "EMI Payment", "sms_info_type": "Outflow", "amount": "2049", "currency": "INR", "emi_amount": "2049", "is_loan_repayment": true, "is_loan_delayed": false}}, {"message": "\"GENTLE REMINDER! Dear DEVWART, EMI of Rs 2049. for your Home Credit Loan ********** is due Tomorrow", "sender": "JK-HOMECR", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "sufficient bank", "currency": "INR"}}, {"message": "Dear DEVWART, in view of the current COVID19, we urge you to stay home. Please maintain sufficient b", "sender": "JD-HOMECR", "parsed_result": {"sms_type": "Payment", "sms_event_subtype": "EMI Payment", "sms_info_type": "Account Status", "amount": "2031", "date": "18-09-2021", "bank_name": "in bank", "currency": "INR", "emi_amount": "2031", "due_date": "18-09-2021", "is_loan_repayment": true, "is_loan_delayed": false}}, {"message": "\"GENTLE REMINDER! Dear DEVWART, EMI of Rs 1841. for your Home Credit Loan ********** is due Tomorrow", "sender": "JD-HOMECR", "parsed_result": {"sms_type": "Payment", "sms_event_subtype": "EMI Payment", "sms_info_type": "Outflow", "amount": "1841", "currency": "INR", "emi_amount": "1841", "is_loan_repayment": true, "is_loan_delayed": false}}, {"message": "\"GENTLE REMINDER! Dear DEVWART, EMI of Rs 1841. for your Home Credit Loan ********** is due Tomorrow", "sender": "JD-HOMECR", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "sufficient bank", "currency": "INR"}}, {"message": "\"GENTLE REMINDER! Dear DEVWART, EMI of Rs 2049. for your Home Credit Loan ********** is due Tomorrow", "sender": "JD-HOMECR", "parsed_result": {"sms_type": "Payment", "sms_event_subtype": "EMI Payment", "sms_info_type": "Outflow", "amount": "2049", "currency": "INR", "emi_amount": "2049", "is_loan_repayment": true, "is_loan_delayed": false}}, {"message": "\"GENTLE REMINDER! Dear DEVWART, EMI of Rs 2049. for your Home Credit Loan ********** is due Tomorrow", "sender": "JD-HOMECR", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "sufficient bank", "currency": "INR"}}, {"message": "\"GENTLE REMINDER! Dear DEVWART, EMI of Rs 2049. for your Home Credit Loan ********** is due Tomorrow", "sender": "MD-HOMECR", "parsed_result": {"sms_type": "Payment", "sms_event_subtype": "EMI Payment", "sms_info_type": "Outflow", "amount": "2049", "currency": "INR", "emi_amount": "2049", "is_loan_repayment": true, "is_loan_delayed": false}}, {"message": "\"GENTLE REMINDER! Dear DEVWART, EMI of Rs 2049. for your Home Credit Loan ********** is due Tomorrow", "sender": "MD-HOMECR", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "sufficient bank", "currency": "INR"}}, {"message": "Dear DEVWART, in view of the current COVID19, we urge you to stay home. Please maintain sufficient b", "sender": "MD-HOMECR", "parsed_result": {"sms_type": "Payment", "sms_event_subtype": "EMI Payment", "sms_info_type": "Account Status", "amount": "2031", "date": "18-07-2021", "bank_name": "in bank", "currency": "INR", "emi_amount": "2031", "due_date": "18-07-2021", "is_loan_repayment": true, "is_loan_delayed": false}}, {"message": "Hi, Payment Reminder! Please pay your pending dues in just 1 click - Log on to <PERSON><PERSON> App. Click on O", "sender": "AX-IDHANI", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "Your Loan Amount for Rs.2,99,999 Is Successfully Processed.\nGet it disbursed into your Bank A/c.\nChe", "sender": "+************", "parsed_result": {"sms_type": "Deposit & Withdrawal", "sms_event_subtype": "<PERSON>an <PERSON>bursal", "sms_info_type": "Inflow", "amount": "299999", "currency": "INR", "loan_id": "Amount"}}, {"message": "Dear Customer, <PERSON><PERSON> Reminder that your monthly Instalment of Rs.9758.00 against Loan No GAJUWT9081", "sender": "JX-STFCTR", "parsed_result": {"sms_type": "Payment", "sms_event_subtype": "EMI Payment", "sms_info_type": "Outflow", "amount": "9758", "date": "20-02-2020", "currency": "INR", "is_loan_repayment": true, "is_loan_delayed": false}}, {"message": "Dear Customer, <PERSON><PERSON> Reminder that your monthly Instalment of Rs.9758.00 against Loan No GAJUWT9081", "sender": "JX-STFCTR", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "bank_name": "your Bank", "currency": "INR"}}, {"message": "Dear VENKATA RAMANA   BADIDABOINA, keep your promise and pay your renewal premium of Rs 3415.00 for ", "sender": "JD-SRILIC", "parsed_result": {"sms_type": "Payment", "sms_event_subtype": "EMI Payment", "sms_info_type": "Outflow", "amount": "3415", "date": "28-08-2020", "currency": "INR", "due_date": "28-08-2020", "is_loan_repayment": false, "is_loan_delayed": false}}, {"message": "Your NACH for Rs. 9758.00 against your Loan Number GAJUWT908160001 for the due date 20-04-2021 has b", "sender": "JKSTFCTR", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Outflow", "amount": "9758", "date": "20-04-2020", "currency": "INR"}}, {"message": "Dear Customer, Total amount overdue on your <PERSON><PERSON>an Account IPXXXXXXXXXXX46 is Rs.5,327.63. EMI -", "sender": "VM-IDHANI", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "5327.63", "currency": "INR"}}, {"message": "Dear Customer, Total amount overdue on your <PERSON><PERSON>an Account IPXXXXXXXXXXX46 is Rs.5,327.63. EMI -", "sender": "VM-IDHANI", "parsed_result": {"sms_type": "Payment", "sms_event_subtype": "EMI Payment", "sms_info_type": "Outflow", "amount": "2000", "currency": "INR", "is_loan_repayment": false, "is_loan_delayed": true}}, {"message": "Dear Customer, Despite our previous reminders, we have not received the overdue payment of Rs 5256.8", "sender": "V<PERSON><PERSON><PERSON><PERSON><PERSON>", "parsed_result": {"sms_type": "Payment", "sms_event_subtype": "EMI Payment", "sms_info_type": "Inflow", "amount": "5256.83", "currency": "INR", "is_loan_repayment": true, "is_loan_delayed": true}}, {"message": "Dear Customer, you have failed to repay the outstanding amounts of Rs.2839 of the loan availed from ", "sender": "JD-KRBEEE", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Loan", "sms_info_type": "Account Status", "amount": "2839", "currency": "INR", "loan_id": "availed"}}, {"message": "Dear Customer, you have failed to repay the outstanding amounts of Rs.2839 of the loan availed from ", "sender": "JD-KRBEEE", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Loan", "sms_info_type": "Outflow", "currency": "INR"}}, {"message": "Dear Customer, you have failed to repay the outstanding amounts of Rs.2839 of the loan availed from ", "sender": "JD-KRBEEE", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Loan", "sms_info_type": "Application", "currency": "INR"}}, {"message": "Dear Customer, you have failed to repay the outstanding amounts of Rs.2839 of the loan availed from ", "sender": "CP-KRBEEE", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Loan", "sms_info_type": "Account Status", "amount": "2839", "currency": "INR", "loan_id": "availed"}}, {"message": "Dear Customer, you have failed to repay the outstanding amounts of Rs.2839 of the loan availed from ", "sender": "CP-KRBEEE", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Loan", "sms_info_type": "Outflow", "currency": "INR"}}, {"message": "Dear Customer, you have failed to repay the outstanding amounts of Rs.2839 of the loan availed from ", "sender": "CP-KRBEEE", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Loan", "sms_info_type": "Application", "currency": "INR"}}], "failed_cases": [{"message": "Your Money View Loan of Rs.10000.0 was disbursed in record time!! Help others make the right choice ", "sender": "AD-MONVEW", "category": "emi_loans"}, {"message": "Your Money View Loan of Rs.10000.0 was disbursed in record time!! Help others make the right choice ", "sender": "AD-MONVEW", "category": "emi_loans"}, {"message": "Your Quick Loan of Rs.10,000/- is Pre-Approved. Get directly Disbursed to your Bank within 5 Mins. \n", "sender": "+************", "category": "emi_loans"}, {"message": "Dear Sir/<PERSON>am, due to your good credit record, the loan credit has been increased! Please get it in", "sender": "QP-GOPAAM", "category": "emi_loans"}]}, "atm_transactions": {"category": "atm_transactions", "tested": 50, "parsed": 50, "filtered": 0, "failed": 0, "fields_extracted": {"sms_info_type": 79, "date": 23, "account_number": 25, "bank_name": 45, "currency": 79, "merchant_name": 16, "card_number": 24, "amount": 28, "txn_ref": 10, "current_amount": 4}, "classifications": {"Purchase:Debit Card": 74, "Accounts:Bank Account": 5}, "edge_cases": [{"message": "Dear SBI Customer, Mini Statement taken from A/cX1899 at SBI ATM S5NE050949621 on 29Apr22. Transacti", "sender": "BX-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "date": "29-04-2022", "account_number": "X1899", "bank_name": "SBI", "currency": "INR", "merchant_name": "SBI ATM S5NE050949621"}}, {"message": "Dear SBI Customer, Mini Statement taken from A/cX1899 at SBI ATM S5NE050949621 on 29Apr22. Transacti", "sender": "BX-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "currency": "INR"}}, {"message": "Dear SBI Customer, Mini Statement taken from A/cX1899 at SBI ATM S5NE050949621 on 29Apr22. Transacti", "sender": "BX-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "bank_name": "SBI", "currency": "INR"}}, {"message": "Dear SBI Customer, PIN CHANGE was done successfully for the card X8981 at ATM S5NE050949621 on 29-Ap", "sender": "BX-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "account_number": "X8981", "bank_name": "SBI", "currency": "INR", "card_number": "X8981", "merchant_name": "ATM S5NE050949621"}}, {"message": "Dear SBI Customer, PIN CHANGE was done successfully for the card X8981 at ATM S5NE050949621 on 29-Ap", "sender": "BX-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "currency": "INR"}}, {"message": "Dear SBI Customer, PIN CHANGE was done successfully for the card X8981 at ATM S5NE050949621 on 29-Ap", "sender": "BX-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "bank_name": "SBI", "currency": "INR"}}, {"message": "CAUTION! Do not share this message. Your One Time PIN for Debit Card ending 8981 is Two Four One One", "sender": "SBIINB", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "currency": "INR", "card_number": "8981"}}, {"message": "CAUTION! Do not share this message. Your One Time PIN for Debit Card ending 8981 is Two Four One One", "sender": "SBIINB", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "bank_name": "SBI", "currency": "INR", "merchant_name": "any SBI ATM"}}, {"message": "Dear SBI Customer, Rs.3000 withdrawn at HDF ATM P3DCCH33 from A/cX2977 on 25Oct21 Transaction Number", "sender": "BX-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "3000", "date": "25-10-2021", "account_number": "X2977", "bank_name": "SBI", "txn_ref": "************", "currency": "INR"}}, {"message": "Dear SBI Customer, Rs.3000 withdrawn at HDF ATM P3DCCH33 from A/cX2977 on 25Oct21 Transaction Number", "sender": "BX-ATMSBI", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "71", "currency": "INR", "current_amount": "71"}}, {"message": "Dear SBI Customer, Rs.3000 withdrawn at HDF ATM P3DCCH33 from A/cX2977 on 25Oct21 Transaction Number", "sender": "BX-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "currency": "INR"}}, {"message": "Dear SBI Customer, Rs.2000 withdrawn at SBI ATM S1NC061067117 from A/cX2977 on 13Oct21 Transaction N", "sender": "BZ-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "2000", "date": "13-10-2021", "account_number": "X2977", "bank_name": "SBI", "txn_ref": "4528", "currency": "INR"}}, {"message": "Dear SBI Customer, Rs.2000 withdrawn at SBI ATM S1NC061067117 from A/cX2977 on 13Oct21 Transaction N", "sender": "BZ-ATMSBI", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "2086", "currency": "INR", "current_amount": "2086"}}, {"message": "Dear SBI Customer, Rs.2000 withdrawn at SBI ATM S1NC061067117 from A/cX2977 on 13Oct21 Transaction N", "sender": "BZ-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "currency": "INR"}}, {"message": "Dear customer, transaction number ************ for Rs16.00 by SBI Debit Card X6613 at ******** on 12", "sender": "BZ-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "16", "date": "12-10-2021", "account_number": "X6613", "bank_name": "SBI", "txn_ref": "************", "currency": "INR", "card_number": "X6613", "merchant_name": "********"}}, {"message": "Dear customer, transaction number ************ for Rs16.00 by SBI Debit Card X6613 at ******** on 12", "sender": "BZ-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "currency": "INR"}}, {"message": "Dear customer, transaction number ************ for Rs16.00 by SBI Debit Card X6613 at ******** on 07", "sender": "BX-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "16", "date": "07-10-2021", "account_number": "X6613", "bank_name": "SBI", "txn_ref": "************", "currency": "INR", "card_number": "X6613", "merchant_name": "********"}}, {"message": "Dear customer, transaction number ************ for Rs16.00 by SBI Debit Card X6613 at ******** on 07", "sender": "BX-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "currency": "INR"}}, {"message": "Dear customer, transaction number ************ for Rs200.00 by SBI Debit Card X6613 at ******** on 1", "sender": "BZ-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "200", "date": "16-08-2021", "account_number": "X6613", "bank_name": "SBI", "txn_ref": "************", "currency": "INR", "card_number": "X6613", "merchant_name": "********"}}, {"message": "Dear customer, transaction number ************ for Rs200.00 by SBI Debit Card X6613 at ******** on 1", "sender": "BZ-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "currency": "INR"}}, {"message": "Dear SBI Customer, Mini Statement taken from A/cX1899 at SBI ATM S5NE050949621 on 29Apr22. Transacti", "sender": "BX-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "date": "29-04-2022", "account_number": "X1899", "bank_name": "SBI", "currency": "INR", "merchant_name": "SBI ATM S5NE050949621"}}, {"message": "Dear SBI Customer, Mini Statement taken from A/cX1899 at SBI ATM S5NE050949621 on 29Apr22. Transacti", "sender": "BX-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "currency": "INR"}}, {"message": "Dear SBI Customer, Mini Statement taken from A/cX1899 at SBI ATM S5NE050949621 on 29Apr22. Transacti", "sender": "BX-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "bank_name": "SBI", "currency": "INR"}}, {"message": "Dear SBI Customer, PIN CHANGE was done successfully for the card X8981 at ATM S5NE050949621 on 29-Ap", "sender": "BX-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "account_number": "X8981", "bank_name": "SBI", "currency": "INR", "card_number": "X8981", "merchant_name": "ATM S5NE050949621"}}, {"message": "Dear SBI Customer, PIN CHANGE was done successfully for the card X8981 at ATM S5NE050949621 on 29-Ap", "sender": "BX-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "currency": "INR"}}, {"message": "Dear SBI Customer, PIN CHANGE was done successfully for the card X8981 at ATM S5NE050949621 on 29-Ap", "sender": "BX-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "bank_name": "SBI", "currency": "INR"}}, {"message": "CAUTION! Do not share this message. Your One Time PIN for Debit Card ending 8981 is Two Four One One", "sender": "SBIINB", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "currency": "INR", "card_number": "8981"}}, {"message": "CAUTION! Do not share this message. Your One Time PIN for Debit Card ending 8981 is Two Four One One", "sender": "SBIINB", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "bank_name": "SBI", "currency": "INR", "merchant_name": "any SBI ATM"}}, {"message": "Dear SBI Customer, Rs.3000 withdrawn at HDF ATM P3DCCH33 from A/cX2977 on 25Oct21 Transaction Number", "sender": "BX-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "3000", "date": "25-10-2021", "account_number": "X2977", "bank_name": "SBI", "txn_ref": "************", "currency": "INR"}}, {"message": "Dear SBI Customer, Rs.3000 withdrawn at HDF ATM P3DCCH33 from A/cX2977 on 25Oct21 Transaction Number", "sender": "BX-ATMSBI", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "71", "currency": "INR", "current_amount": "71"}}, {"message": "Dear SBI Customer, Rs.3000 withdrawn at HDF ATM P3DCCH33 from A/cX2977 on 25Oct21 Transaction Number", "sender": "BX-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "currency": "INR"}}, {"message": "Dear SBI Customer, Rs.2000 withdrawn at SBI ATM S1NC061067117 from A/cX2977 on 13Oct21 Transaction N", "sender": "BZ-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "2000", "date": "13-10-2021", "account_number": "X2977", "bank_name": "SBI", "txn_ref": "4528", "currency": "INR"}}, {"message": "Dear SBI Customer, Rs.2000 withdrawn at SBI ATM S1NC061067117 from A/cX2977 on 13Oct21 Transaction N", "sender": "BZ-ATMSBI", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "2086", "currency": "INR", "current_amount": "2086"}}, {"message": "Dear SBI Customer, Rs.2000 withdrawn at SBI ATM S1NC061067117 from A/cX2977 on 13Oct21 Transaction N", "sender": "BZ-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "currency": "INR"}}, {"message": "Dear customer, transaction number ************ for Rs16.00 by SBI Debit Card X6613 at ******** on 12", "sender": "BZ-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "16", "date": "12-10-2021", "account_number": "X6613", "bank_name": "SBI", "txn_ref": "************", "currency": "INR", "card_number": "X6613", "merchant_name": "********"}}, {"message": "Dear customer, transaction number ************ for Rs16.00 by SBI Debit Card X6613 at ******** on 12", "sender": "BZ-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "currency": "INR"}}, {"message": "Dear customer, transaction number ************ for Rs16.00 by SBI Debit Card X6613 at ******** on 07", "sender": "BX-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "16", "date": "07-10-2021", "account_number": "X6613", "bank_name": "SBI", "txn_ref": "************", "currency": "INR", "card_number": "X6613", "merchant_name": "********"}}, {"message": "Dear customer, transaction number ************ for Rs16.00 by SBI Debit Card X6613 at ******** on 07", "sender": "BX-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "currency": "INR"}}, {"message": "Dear customer, transaction number ************ for Rs200.00 by SBI Debit Card X6613 at ******** on 1", "sender": "BZ-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "200", "date": "16-08-2021", "account_number": "X6613", "bank_name": "SBI", "txn_ref": "************", "currency": "INR", "card_number": "X6613", "merchant_name": "********"}}, {"message": "Dear customer, transaction number ************ for Rs200.00 by SBI Debit Card X6613 at ******** on 1", "sender": "BZ-ATMSBI", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "currency": "INR"}}], "failed_cases": []}, "cash_deposits": {"category": "cash_deposits", "tested": 2, "parsed": 2, "filtered": 0, "failed": 0, "fields_extracted": {"sms_info_type": 2, "amount": 2, "bank_name": 2, "currency": 2}, "classifications": {"Purchase:Debit Card": 2}, "edge_cases": [{"message": "Cash deposit of Rs 4500.00 to Acct ************ has been initiated through ICICI Bank INSTA Banking.", "sender": "VK-ICICIB", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "4500", "bank_name": "ICICI", "currency": "INR"}}, {"message": "Cash deposit of Rs 4500.00 to Acct ************ has been initiated through ICICI Bank INSTA Banking.", "sender": "VK-ICICIB", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "4500", "bank_name": "ICICI", "currency": "INR"}}], "failed_cases": []}, "paytm_transactions": {"category": "paytm_transactions", "tested": 38, "parsed": 38, "filtered": 0, "failed": 0, "fields_extracted": {"sms_info_type": 81, "amount": 46, "date": 1, "account_number": 20, "currency": 81, "bank_name": 19}, "classifications": {"Accounts:Bank Account": 1, "Deposit & Withdrawal:Payment Received": 38, "Purchase:UPI": 42}, "edge_cases": [{"message": "Paid Rs.470.82 to Vodafone Idea Ltd from Paytm Balance. Updated Balance: Paytm Wallet- Rs 0. More De", "sender": "AX-iPaytm", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "amount": "0", "currency": "INR"}}, {"message": "Paid Rs.352.82 to Vodafone Idea Ltd from Paytm Balance. Updated Balance: Paytm Wallet- Rs 0. More De", "sender": "AX-iPaytm", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "amount": "0", "currency": "INR"}}, {"message": "Paid Rs.352.82 to Vodafone Idea Ltd from Paytm Balance. Updated Balance: Paytm Wallet- Rs 0. More De", "sender": "AX-iPaytm", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "amount": "0", "currency": "INR"}}, {"message": "Paid Rs.395.3 to Vodafone Idea Ltd from Paytm Balance. Updated Balance: Paytm Wallet- Rs 0. More Det", "sender": "AD-iPaytm", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "amount": "0", "currency": "INR"}}, {"message": "Paid Rs.352.82 to Vodafone Idea Ltd from Paytm Balance. Updated Balance: Paytm Wallet- Rs 0. More De", "sender": "AD-iPaytm", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "amount": "0", "currency": "INR"}}, {"message": "Paid Rs.388.22 to Vodafone Idea Ltd from Paytm Balance. Updated Balance: Paytm Wallet- Rs 0. More De", "sender": "AX-iPaytm", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "amount": "0", "currency": "INR"}}, {"message": "Paid Rs.470.82 to Vodafone Idea Ltd from Paytm Balance. Updated Balance: Paytm Wallet- Rs 0. More De", "sender": "AX-iPaytm", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "amount": "0", "currency": "INR"}}, {"message": "Paid Rs.352.82 to Vodafone Idea Ltd from Paytm Balance. Updated Balance: Paytm Wallet- Rs 0. More De", "sender": "AX-iPaytm", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "amount": "0", "currency": "INR"}}, {"message": "Paid Rs.352.82 to Vodafone Idea Ltd from Paytm Balance. Updated Balance: Paytm Wallet- Rs 0. More De", "sender": "AX-iPaytm", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "amount": "0", "currency": "INR"}}, {"message": "Paid Rs.395.3 to Vodafone Idea Ltd from Paytm Balance. Updated Balance: Paytm Wallet- Rs 0. More Det", "sender": "AD-iPaytm", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "amount": "0", "currency": "INR"}}, {"message": "Paid Rs.352.82 to Vodafone Idea Ltd from Paytm Balance. Updated Balance: Paytm Wallet- Rs 0. More De", "sender": "AD-iPaytm", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "amount": "0", "currency": "INR"}}, {"message": "Paid Rs.388.22 to Vodafone Idea Ltd from Paytm Balance. Updated Balance: Paytm Wallet- Rs 0. More De", "sender": "AX-iPaytm", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "amount": "0", "currency": "INR"}}], "failed_cases": []}, "toll_payments": {"category": "toll_payments", "tested": 12, "parsed": 12, "filtered": 0, "failed": 0, "fields_extracted": {"sms_info_type": 17, "amount": 17, "date": 12, "bank_name": 5, "currency": 17, "merchant_name": 12}, "classifications": {"Purchase:Debit Card": 12, "Accounts:Bank Account": 5}, "edge_cases": [{"message": "Dear Customer, ICICI Bank FASTag linked vehicle no. AP39TH2327 has been debited with Rs 10.00 on 21-", "sender": "TX-ICICIB", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "10", "date": "21-08-2020", "currency": "INR", "merchant_name": "Goshtani Gate Toll Plaza Plaza"}}, {"message": "Dear Customer, ICICI Bank FASTag linked vehicle no. AP39TH2327 has been debited with Rs 55.00 on 20-", "sender": "JM-ICICIB", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "55", "date": "20-08-2020", "currency": "INR", "merchant_name": "Agnampadi Toll Plaza Plaza"}}, {"message": "Dear Customer, ICICI Bank FASTag linked vehicle no. AP39TH2327 has been debited with Rs 30.00 on 11-", "sender": "MD-ICICIB", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "30", "date": "11-08-2020", "currency": "INR", "merchant_name": "Madapam Toll Plaza Plaza"}}, {"message": "Dear Customer, ICICI Bank FASTag linked vehicle no. AP39TH2327 has been debited with Rs 30.00 on 11-", "sender": "MD-ICICIB", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "259.88", "currency": "INR"}}, {"message": "Dear Customer, ICICI Bank FASTag linked vehicle no. AP39TH2327 has been debited with Rs 10.00 on 23-", "sender": "MD-ICICIB", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "10", "date": "23-07-2020", "currency": "INR", "merchant_name": "Goshtani Gate Toll Plaza Plaza"}}, {"message": "Dear Customer, ICICI Bank FASTag linked vehicle no. AP39TH2327 has been debited with Rs 10.00 on 23-", "sender": "MD-ICICIB", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "104.88", "currency": "INR"}}, {"message": "Dear Customer, ICICI Bank FASTag linked vehicle no. AP39TH2327 has been debited with Rs 30.00 on 19-", "sender": "MD-ICICIB", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "30", "date": "19-07-2020", "currency": "INR", "merchant_name": "Madapam Toll Plaza Plaza"}}, {"message": "Dear Customer, ICICI Bank FASTag linked vehicle no. AP39TH2327 has been debited with Rs 30.00 on 19-", "sender": "MD-ICICIB", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "184.88", "currency": "INR"}}, {"message": "Dear Customer, ICICI Bank FASTag linked vehicle no. AP39TH2327 has been debited with Rs 25.00 on 21-", "sender": "MDICICIB", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "25", "date": "21-03-2020", "currency": "INR", "merchant_name": "Agnampadi Toll Plaza Plaza"}}, {"message": "Dear Customer, ICICI Bank FASTag linked vehicle no. AP39TH2327 has been debited with Rs 25.00 on 21-", "sender": "MDICICIB", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "342.88", "currency": "INR"}}, {"message": "Dear Customer, ICICI Bank FASTag linked vehicle no. AP39TH2327 has been debited with Rs 55.00 on 21-", "sender": "MDICICIB", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "55", "date": "21-03-2020", "currency": "INR", "merchant_name": "Agnampadi Toll Plaza Plaza"}}, {"message": "Dear Customer, ICICI Bank FASTag linked vehicle no. AP39TH2327 has been debited with Rs 55.00 on 21-", "sender": "MDICICIB", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "367.88", "currency": "INR"}}], "failed_cases": []}, "razorpay_transactions": {"category": "razorpay_transactions", "tested": 20, "parsed": 20, "filtered": 0, "failed": 0, "fields_extracted": {"sms_info_type": 22, "amount": 20, "currency": 22, "date": 2, "account_number": 2, "txn_ref": 2, "card_number": 2}, "classifications": {"Accounts:Bank Account": 20, "Purchase:Debit Card": 2}, "edge_cases": [], "failed_cases": []}, "credit_reversals": {"category": "credit_reversals", "tested": 9, "parsed": 9, "filtered": 0, "failed": 0, "fields_extracted": {"sms_info_type": 15, "amount": 7, "bank_name": 7, "currency": 15}, "classifications": {"Purchase:Debit Card": 14, "Accounts:Bank Account": 1}, "edge_cases": [{"message": "Hi, finance charge of INR 6.00 has been reversed on your Bajaj Finserv RBL Bank SuperCard (XXXX07). ", "sender": "VD-SPRCRD", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "6", "bank_name": "RBL Bank", "currency": "INR"}}, {"message": "Hi, finance charge of INR 6.00 has been reversed on your Bajaj Finserv RBL Bank SuperCard (XXXX07). ", "sender": "VD-SPRCRD", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "currency": "INR"}}, {"message": "Hi, finance charge of INR 33.00 has been reversed on your Bajaj Finserv RBL Bank SuperCard (XXXX07).", "sender": "VD-SPRCRD", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "33", "bank_name": "RBL Bank", "currency": "INR"}}, {"message": "Hi, finance charge of INR 33.00 has been reversed on your Bajaj Finserv RBL Bank SuperCard (XXXX07).", "sender": "VD-SPRCRD", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "currency": "INR"}}, {"message": "Hi, Late Payment Fee of INR 177.00 has been reversed on your Bajaj Finserv RBL Bank SuperCard (XXXX0", "sender": "AX-SPRCRD", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "177", "bank_name": "RBL Bank", "currency": "INR"}}, {"message": "Hi, Late Payment Fee of INR 177.00 has been reversed on your Bajaj Finserv RBL Bank SuperCard (XXXX0", "sender": "AX-SPRCRD", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "currency": "INR"}}, {"message": "Hi, finance charge of INR 6.00 has been reversed on your Bajaj Finserv RBL Bank SuperCard (XXXX07). ", "sender": "VD-SPRCRD", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "6", "bank_name": "RBL Bank", "currency": "INR"}}, {"message": "Hi, finance charge of INR 6.00 has been reversed on your Bajaj Finserv RBL Bank SuperCard (XXXX07). ", "sender": "VD-SPRCRD", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "currency": "INR"}}, {"message": "Hi, finance charge of INR 33.00 has been reversed on your Bajaj Finserv RBL Bank SuperCard (XXXX07).", "sender": "VD-SPRCRD", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "33", "bank_name": "RBL Bank", "currency": "INR"}}, {"message": "Hi, finance charge of INR 33.00 has been reversed on your Bajaj Finserv RBL Bank SuperCard (XXXX07).", "sender": "VD-SPRCRD", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "currency": "INR"}}, {"message": "Hi, Late Payment Fee of INR 177.00 has been reversed on your Bajaj Finserv RBL Bank SuperCard (XXXX0", "sender": "AX-SPRCRD", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "177", "bank_name": "RBL Bank", "currency": "INR"}}, {"message": "Hi, Late Payment Fee of INR 177.00 has been reversed on your Bajaj Finserv RBL Bank SuperCard (XXXX0", "sender": "AX-SPRCRD", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "currency": "INR"}}], "failed_cases": []}, "otp_messages": {"category": "otp_messages", "tested": 50, "parsed": 0, "filtered": 50, "failed": 0, "fields_extracted": {}, "classifications": {}, "edge_cases": [], "failed_cases": []}, "promotional_messages": {"category": "promotional_messages", "tested": 50, "parsed": 0, "filtered": 50, "failed": 0, "fields_extracted": {}, "classifications": {}, "edge_cases": [], "failed_cases": []}, "recharge_offers": {"category": "recharge_offers", "tested": 1, "parsed": 0, "filtered": 1, "failed": 0, "fields_extracted": {}, "classifications": {}, "edge_cases": [], "failed_cases": []}, "gaming_offers": {"category": "gaming_offers", "tested": 3, "parsed": 0, "filtered": 3, "failed": 0, "fields_extracted": {}, "classifications": {}, "edge_cases": [], "failed_cases": []}, "welcome_messages": {"category": "welcome_messages", "tested": 50, "parsed": 0, "filtered": 50, "failed": 0, "fields_extracted": {}, "classifications": {}, "edge_cases": [], "failed_cases": []}, "bill_reminders": {"category": "bill_reminders", "tested": 8, "parsed": 8, "filtered": 0, "failed": 0, "fields_extracted": {"sms_info_type": 8, "amount": 8, "currency": 8, "default_status": 8}, "classifications": {"Accounts:Loan": 8}, "edge_cases": [{"message": "Alert! Bill of Rs  1095.54 is overdue against Vi mobile no.  Mobile no  ********** with relationship", "sender": "VG-ViCARE", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Loan", "sms_info_type": "Account Status", "amount": "1095.54", "currency": "INR", "default_status": "overdue"}}, {"message": "Alert! Bill of Rs  1095.54 is overdue against Vi mobile no.  Mobile no  ********** with relationship", "sender": "VG-ViCARE", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Loan", "sms_info_type": "Account Status", "amount": "1095.54", "currency": "INR", "default_status": "overdue"}}, {"message": "Alert! Bill of Rs  947.16 is overdue against Vi mobile no.  Mobile no  ********** with relationship ", "sender": "VV-ViCARE", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Loan", "sms_info_type": "Account Status", "amount": "947.16", "currency": "INR", "default_status": "overdue"}}, {"message": "Alert! Bill of Rs  947.16 is overdue against Vi mobile no.  Mobile no  ********** with relationship ", "sender": "VG-ViCARE", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Loan", "sms_info_type": "Account Status", "amount": "947.16", "currency": "INR", "default_status": "overdue"}}, {"message": "Alert! Bill of Rs  947.16 is overdue against Vi mobile no.  Mobile no  ********** with relationship ", "sender": "VG-ViCARE", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Loan", "sms_info_type": "Account Status", "amount": "947.16", "currency": "INR", "default_status": "overdue"}}, {"message": "Alert! Bill of Rs  947.16 is overdue against Vi mobile no.  Mobile no  ********** with relationship ", "sender": "VG-ViCARE", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Loan", "sms_info_type": "Account Status", "amount": "947.16", "currency": "INR", "default_status": "overdue"}}, {"message": "Alert! Bill of Rs  947.16 is overdue against Vi mobile no.  Mobile no  ********** with relationship ", "sender": "VG-ViCARE", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Loan", "sms_info_type": "Account Status", "amount": "947.16", "currency": "INR", "default_status": "overdue"}}, {"message": "Alert! Bill of Rs  947.16 is overdue against Vi mobile no.  Mobile no  ********** with relationship ", "sender": "VG-ViCARE", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Loan", "sms_info_type": "Account Status", "amount": "947.16", "currency": "INR", "default_status": "overdue"}}], "failed_cases": []}, "job_offers": {"category": "job_offers", "tested": 50, "parsed": 0, "filtered": 50, "failed": 0, "fields_extracted": {}, "classifications": {}, "edge_cases": [], "failed_cases": []}, "complex_multi_event": {"category": "complex_multi_event", "tested": 50, "parsed": 50, "filtered": 0, "failed": 0, "fields_extracted": {"sms_info_type": 76, "amount": 72, "date": 50, "account_number": 50, "currency": 76, "merchant_name": 18, "bank_name": 50, "current_amount": 26, "txn_ref": 21}, "classifications": {"Purchase:Debit Card": 19, "Accounts:Bank Account": 57}, "edge_cases": [], "failed_cases": []}}, "field_extraction_stats": {"sms_info_type": 669, "bank_name": 269, "currency": 669, "amount": 396, "account_number": 205, "txn_ref": 108, "upi_recipient": 8, "date": 128, "card_number": 30, "merchant_name": 48, "default_status": 10, "current_amount": 66, "loan_id": 10, "is_loan_repayment": 30, "emi_amount": 20, "due_date": 8, "is_loan_delayed": 2}, "classification_stats": {"Accounts:Bank Account": 275, "Purchase:UPI": 113, "Purchase:Debit Card": 148, "Accounts:Loan": 17, "Accounts:Promotional Credit": 4, "Investment:Investment": 32, "Deposit & Withdrawal:Loan Disbursal": 7, "Payment:EMI Payment": 35, "Deposit & Withdrawal:Payment Received": 38}, "filtering_stats": {"upi_transactions": 0, "hdfc_transactions": 0, "simpl_payments": 0, "jio_recharges": 5, "stock_exchange": 0, "emi_loans": 0, "atm_transactions": 0, "cash_deposits": 0, "paytm_transactions": 0, "toll_payments": 0, "razorpay_transactions": 0, "credit_reversals": 0, "otp_messages": 50, "promotional_messages": 50, "recharge_offers": 1, "gaming_offers": 3, "welcome_messages": 50, "bill_reminders": 0, "job_offers": 50, "complex_multi_event": 0}, "edge_cases": [{"message": "Your A/c XXX1824658  is credited by Rs. 6,000 Total Bal : Rs. 7,020.35 CR  Clr Bal : Rs. 7,020.35 CR", "sender": "BV-INDBNK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Inflow", "amount": "7020.35", "date": "13-04-2022", "account_number": "XXX182465", "currency": "INR"}}, {"message": "UPDATE: INR 1,00,000.00 debited from HDFC Bank XX0930 on 07-JAN-22. Info: WITHDRAWAL SLIP NO. 2356/1", "sender": "QP-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "142761.44", "currency": "INR"}}, {"message": "UPDATE: INR 5,00,000.00 debited from HDFC Bank XX3594 on 14-OCT-21. Info: LOOSE LEAF-477 RAM SINGH S", "sender": "AD-HDFCBK", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "500000", "account_number": "XX3594", "bank_name": "HDFC", "currency": "INR"}}, {"message": "UPDATE: INR 5,00,000.00 debited from HDFC Bank XX3594 on 14-OCT-21. Info: LOOSE LEAF-477 RAM SINGH S", "sender": "AD-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "1029659.2", "currency": "INR"}}, {"message": "UPDATE: INR 5,26,000.00 debited from HDFC Bank XX3594 on 13-OCT-21. Info: FT - Dr - XXXXXXXXXX4398 -", "sender": "AD-HDFCBK", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "526000", "account_number": "XX3594", "bank_name": "HDFC", "currency": "INR"}}, {"message": "UPDATE: INR 5,26,000.00 debited from HDFC Bank XX3594 on 13-OCT-21. Info: FT - Dr - XXXXXXXXXX4398 -", "sender": "AD-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "1529659.2", "currency": "INR"}}, {"message": "UPDATE: INR 49,50,000.00 debited from HDFC Bank XX3594 on 13-OCT-21. Info: FT - Dr - XXXXXXXXXX6521 ", "sender": "QP-HDFCBK", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "4950000", "account_number": "XX3594", "bank_name": "HDFC", "currency": "INR"}}, {"message": "UPDATE: INR 49,50,000.00 debited from HDFC Bank XX3594 on 13-OCT-21. Info: FT - Dr - XXXXXXXXXX6521 ", "sender": "QP-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "2055659.2", "currency": "INR"}}, {"message": "UPDATE: INR 11,800.00 debited from HDFC Bank XX3594 on 16-SEP-21. Info: MC CHARGES INCL GST 080921. ", "sender": "AD-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "6913598.2", "currency": "INR"}}, {"message": "UPDATE: INR 42,00,000.00 debited from HDFC Bank XX3594 on 08-SEP-21. Info: MC Issued - KAMBALA - 901", "sender": "AX-HDFCBK", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "4200000", "account_number": "XX3594", "bank_name": "HDFC", "currency": "INR"}}, {"message": "UPDATE: INR 42,00,000.00 debited from HDFC Bank XX3594 on 08-SEP-21. Info: MC Issued - KAMBALA - 901", "sender": "AX-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "6975398.2", "currency": "INR"}}, {"message": "UPDATE: INR 2,00,000.00 debited from HDFC Bank XX0930 on 27-AUG-21. Info: 2356/1107, RAM SINGH - CAS", "sender": "JK-HDFCBK", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "200000", "account_number": "XX0930", "bank_name": "HDFC", "currency": "INR"}}, {"message": "UPDATE: INR 2,00,000.00 debited from HDFC Bank XX0930 on 27-AUG-21. Info: 2356/1107, RAM SINGH - CAS", "sender": "JK-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "120065.98", "currency": "INR"}}, {"message": "UPDATE: INR 40,000.00 debited from HDFC Bank XX0930 on 16-AUG-21. Info: FT -KULWINDER SINGH Dr - XXX", "sender": "JD-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "270065.98", "currency": "INR"}}, {"message": "UPDATE: INR 5,00,000.00 debited from HDFC Bank XX3594 on 16-AUG-21. Info: FT - Dr - XXXXXXXXXX5201 -", "sender": "JD-HDFCBK", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "500000", "account_number": "XX3594", "bank_name": "HDFC", "currency": "INR"}}, {"message": "UPDATE: INR 5,00,000.00 debited from HDFC Bank XX3594 on 16-AUG-21. Info: FT - Dr - XXXXXXXXXX5201 -", "sender": "JD-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "********.2", "currency": "INR"}}, {"message": "UPDATE: INR 52,250.00 debited from HDFC Bank XX0930 on 05-AUG-21. Info: HLICCON - ************* - By", "sender": "MD-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "460065.98", "currency": "INR"}}, {"message": "UPDATE: INR 1,00,000.00 debited from A/c XX3594 on 15-JUN-21. Info: slip no 436/9011,cash withdrawal", "sender": "MD-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "********.9", "currency": "INR"}}, {"message": "UPDATE: INR 5,00,000.00 debited from A/c XX3594 on 20-MAY-21. Info: slip no 426/9011,cash withdrawal", "sender": "MD-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Outflow", "amount": "500000", "account_number": "XX3594", "currency": "INR"}}, {"message": "UPDATE: INR 5,00,000.00 debited from A/c XX3594 on 20-MAY-21. Info: slip no 426/9011,cash withdrawal", "sender": "MD-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "********.9", "currency": "INR"}}, {"message": "UPDATE: INR 4,00,000.00 debited from A/c XX3594 on 23-APR-21. Info: RTGS Dr-PSIB0000230-SUCHA SINGH-", "sender": "VK-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Outflow", "amount": "400000", "account_number": "XX3594", "currency": "INR"}}, {"message": "UPDATE: INR 4,00,000.00 debited from A/c XX3594 on 23-APR-21. Info: RTGS Dr-PSIB0000230-SUCHA SINGH-", "sender": "VK-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "1510628.6", "currency": "INR"}}, {"message": "UPDATE: INR 4,00,000.00 debited from A/c XX3594 on 23-APR-21. Info: FT - Dr - XXXXXXXXXX2551 - RAVI ", "sender": "VK-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Outflow", "amount": "400000", "account_number": "XX3594", "currency": "INR"}}, {"message": "UPDATE: INR 4,00,000.00 debited from A/c XX3594 on 23-APR-21. Info: FT - Dr - XXXXXXXXXX2551 - RAVI ", "sender": "VK-HDFCBK", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "amount": "1910628.6", "currency": "INR"}}, {"message": "Rs.209.0 for Vodafone Idea, Mobile Number **********, on 6 May 2022 11:59 PM charged to your Simpl a", "sender": "CP-SmplPL", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Outflow", "amount": "209", "currency": "INR"}}, {"message": "Rs.209.0 for Vodafone Idea, Mobile Number **********, on 6 May 2022 11:59 PM charged to your Simpl a", "sender": "CP-SmplPL", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "Rs.209.0 for Vodafone Idea, Mobile Number **********, on 6 May 2022 11:59 PM charged to your Simpl a", "sender": "CP-SmplPL", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "Debit Card", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "Alert! Your Vi bill of Rs.315.86 is overdue. Please make an immediate payment to enjoy uninterrupted", "sender": "VK-ViCARE", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Loan", "sms_info_type": "Account Status", "amount": "315.86", "currency": "INR", "default_status": "overdue"}}, {"message": "Alert! Your Vi bill of Rs.315.86 is overdue.To enjoy uninterrupted services, pay now using Vi App or", "sender": "VK-ViCARE", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Loan", "sms_info_type": "Account Status", "amount": "315.86", "currency": "INR", "default_status": "overdue"}}, {"message": "90% daily data quota used as on 03-May-22 00:44.\nJio Number : **********\nFor tips on how to manage d", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "Recharge of Rs. 15.0 is successful for your Jio number **********.\nEntitlement: Benefits: Unlimited ", "sender": "JD-JioPay", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Outflow", "amount": "15", "currency": "INR"}}, {"message": "Recharge of Rs. 15.0 is successful for your Jio number **********.\nEntitlement: Benefits: Unlimited ", "sender": "JD-JioPay", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "90% daily data quota used as on 28-Apr-22 16:37.\nJio Number : **********\nFor tips on how to manage d", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "90% daily data quota used as on 27-Apr-22 23:44.\nJio Number : **********\nFor tips on how to manage d", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "90% daily data quota used as on 27-Apr-22 00:27.\nJio Number : **********\nFor tips on how to manage d", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "90% daily data quota used as on 22-Apr-22 20:23.\nJio Number : **********\nFor tips on how to manage d", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "Plan expired! Recharge now on Paytm & get upto Rs.100 Cashback - Code JIO100 each time for self & fa", "sender": "JM-620016", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Outflow", "amount": "100", "currency": "INR", "upi_recipient": "Rs"}}, {"message": "Plan expired! Recharge now on Paytm & get upto Rs.100 Cashback - Code JIO100 each time for self & fa", "sender": "JM-620016", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Promotional Credit", "sms_info_type": "Account Status", "amount": "30", "currency": "INR"}}, {"message": "Plan expired! Recharge now on Paytm & get upto Rs.100 Cashback - Code JIO100 each time for self & fa", "sender": "JM-620016", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Outflow", "amount": "239", "currency": "INR"}}, {"message": "Recharge of Rs. 239.00 is successful for your Jio number **********.\nEntitlement: Benefits: \n1. UNLI", "sender": "JD-JioPay", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Outflow", "amount": "239", "currency": "INR"}}, {"message": "Recharge of Rs. 239.00 is successful for your Jio number **********.\nEntitlement: Benefits: \n1. UNLI", "sender": "JD-JioPay", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "Recharge of Rs. 239.00 is successful for your Jio number **********.\nEntitlement: Benefits: \n1. UNLI", "sender": "JD-JioPay", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Bank Account", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "Hurry! Recharge Jio no.********** on Amazon & get upto Rs.300 daily rewards for self & family. Upto ", "sender": "JK-620016", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Outflow", "amount": "300", "currency": "INR", "upi_recipient": "Rs"}}, {"message": "Hurry! Recharge Jio no.********** on Amazon & get upto Rs.300 daily rewards for self & family. Upto ", "sender": "JK-620016", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Promotional Credit", "sms_info_type": "Account Status", "amount": "50", "currency": "INR"}}, {"message": "Hurry! Recharge Jio no.********** on Amazon & get upto Rs.300 daily rewards for self & family. Upto ", "sender": "JK-620016", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Outflow", "amount": "239", "currency": "INR"}}, {"message": "Your plan Rs 239-1m-1.5GB/D for Jio Number ********** has expired on 19-Apr-22 09:25 Hrs. To continu", "sender": "JP-JIOPAY", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Outflow", "currency": "INR"}}, {"message": "90% daily data quota used as on 18-Apr-22 22:54.\nJio Number : **********\nFor tips on how to manage d", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "currency": "INR"}}, {"message": "Hurry! Recharge Jio no.********** on Paytm & get upto Rs.100 Cashback - Code JIO100 each time for se", "sender": "JX-620016", "parsed_result": {"sms_type": "Accounts", "sms_event_subtype": "Promotional Credit", "sms_info_type": "Account Status", "amount": "30", "currency": "INR"}}, {"message": "Hurry! Recharge Jio no.********** on Paytm & get upto Rs.100 Cashback - Code JIO100 each time for se", "sender": "JX-620016", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Outflow", "amount": "239", "currency": "INR"}}, {"message": "90% daily data quota used as on 17-Apr-22 16:55.\nJio Number : **********\nFor tips on how to manage d", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsed_result": {"sms_type": "Purchase", "sms_event_subtype": "UPI", "sms_info_type": "Account Status", "currency": "INR"}}], "failed_cases": [{"message": "Alert! Your Vi bill of Rs.315.86 was due on 02-03-2022. Please pay via Vi App or to pay online, clic", "sender": "VK-ViCARE", "category": "simpl_payments"}, {"message": "Hi! Your Vi bill of Rs.315.86 is due today 02-03-2022. To pay now via Vi App or to pay online, click", "sender": "VK-ViCARE", "category": "simpl_payments"}, {"message": "Hi! Your Vi bill of Rs.315.86 is due on 02-03-2022. Please pay by due date to avoid late payment cha", "sender": "VK-ViCARE", "category": "simpl_payments"}, {"message": "50% Daily Data quota used as on 02-May-22 20:15.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 02-May-22 01:07.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 01-May-22 00:41.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 29-Apr-22 23:40.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 28-Apr-22 14:15.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 27-Apr-22 14:54.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 26-Apr-22 18:07.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 25-Apr-22 21:17.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 24-Apr-22 21:40.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 23-Apr-22 18:25.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 22-Apr-22 14:31.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 21-Apr-22 19:43.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 20-Apr-22 19:45.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 18-Apr-22 20:17.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "Your current plan Rs 239-1m-1.5GB/D for Jio number ********** will expire on 19-Apr-22 09:25 Hrs. Af", "sender": "JP-JIOPAY", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 17-Apr-22 14:03.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 15-Apr-22 12:27.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 14-Apr-22 22:18.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 14-Apr-22 00:36.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 12-Apr-22 23:34.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "You have consumed 50% of the daily 100.0 Units SMS quota from Rs 239-1m-1.5GB/D on Jio Number 628036", "sender": "JP-JIOPAY", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 11-Apr-22 20:46.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 09-Apr-22 23:30.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "50% Daily Data quota used as on 08-Apr-22 17:09.\nJio Number : **********\nDaily Data quota as per pla", "sender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "jio_recharges"}, {"message": "Beware while dealing based on unsolicited tips through Whatsapp, telegram, SMS, calls, etc. and take", "sender": "VM-NSESMS", "category": "stock_exchange"}, {"message": "Beware while dealing based on unsolicited tips through Whatsapp, telegram, SMS, calls, etc. and take", "sender": "VM-NSESMS", "category": "stock_exchange"}, {"message": "Your Money View Loan of Rs.10000.0 was disbursed in record time!! Help others make the right choice ", "sender": "AD-MONVEW", "category": "emi_loans"}, {"message": "Your Money View Loan of Rs.10000.0 was disbursed in record time!! Help others make the right choice ", "sender": "AD-MONVEW", "category": "emi_loans"}, {"message": "Your Quick Loan of Rs.10,000/- is Pre-Approved. Get directly Disbursed to your Bank within 5 Mins. \n", "sender": "+************", "category": "emi_loans"}, {"message": "Dear Sir/<PERSON>am, due to your good credit record, the loan credit has been increased! Please get it in", "sender": "QP-GOPAAM", "category": "emi_loans"}]}